
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import { useTranslation } from "react-i18next";


const ConsolidateBlock = ({miniBlock}) => {
    const {t} = useTranslation();

    return (
        <MDBox
            position="relative"
            minHeight={miniBlock ? "30vh" : "65vh"}
            height={miniBlock ? "30vh" : "65vh"}
            display="flex"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
            borderRadius="xl"
            my={2}
        >
            <MDBox 
                display="flex"
                flexDirection="column"
                alignItems="center"
                width="100%"
                justifyContent="center">
                <MDBox
                    component="img"
                    pl={3}
                    sx={{
                        width: miniBlock ? "18%" : "30%",
                    }}
                    src={"https://illustrations.popsy.co/white/hitchhiking.svg"}
                    alt={"section-na"}
                />
                <MDTypography variant={miniBlock ? "h6" : "h4"} color="dark" textAlign="center" width={"55%"} mt={2} textTransform="capitalize">
                    {t("section-na")}
                </MDTypography>
                <MDTypography variant={miniBlock ? "button" : "h6"} color="dark" textAlign="center" fontWeight="regular" width={"55%"} mt={1}>
                    {t("consolidated-view-block-1")}
                </MDTypography>
                <MDTypography variant={miniBlock ? "button" : "h6"} color="dark" textAlign="center" fontWeight="regular" width={"55%"} mt={1}>
                    {t("consolidated-view-block-2")}
                </MDTypography>
            </MDBox>
        </MDBox>
    );
}

export default ConsolidateBlock;