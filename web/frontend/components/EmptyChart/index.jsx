

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import emptySvg from "@/assets/images/illustrations/empty-chart.svg";
import {useTranslation} from "react-i18next";

const EmptyChart = ({description}) => {
    const {t} = useTranslation();
    return (
        <MDBox
            display="flex"
            justifyContent="center"
            alignItems="center"
            minHeight="300px"
            height={"100%"}
            flexDirection="column"
        >
            <MDBox mt={2} width="50%">
                <img
                    src={emptySvg}
                    height="180px"
                    alt="empty"
                    width="100%" />
            </MDBox>
            <MDTypography variant="h6" color="secondary" fontWeight="regular" mt={2}>
                {!!description ? description : t("empty-chart")}
            </MDTypography>
        </MDBox>
    )
}

export default EmptyChart;