import React, {useMemo, useState, useEffect} from "react";
// antd imports
import {DatePicker, Space} from "antd";
import dayjs from 'dayjs';
import PaywallDialog from '@/components/Paywall';
import NiceModal from '@ebay/nice-modal-react';
import {useMaterialUIController, setSelectedFilters, tracker} from "@/context";
import MDTypography from "@/components/MDTypography";
import MDBox from "@/components/MDBox";
import premiumTag from "@/assets/images/premium-tag.png";
import {useTranslation} from "react-i18next";
import updateLocale from 'dayjs/plugin/updateLocale';
import Icon from "@mui/material/Icon";
import MDTooltip from "@/components/MDTooltip";

dayjs.extend(updateLocale)
dayjs.updateLocale('en', {
    weekStart: 1
})


function ComparisonDatePicker(props) {

    const [controller, dispatch] = useMaterialUIController();
    const {selectedFilters, shopConfig} = controller;
    const {compare, start_date, end_date, compare_start_date, compare_end_date} = selectedFilters;
    const [prevOpen, setPrevOpen] = useState(false);
    const { RangePicker } = DatePicker;
    const dateFormat = "D MMM YYYY";
    const {t} = useTranslation();

    const checkIfPremiumRequired = (start) => {
        let isSubscriptionEnabled = (shopConfig.subscription_enabled ?? false) 
        if (!start || !isSubscriptionEnabled) {
            return false;
        }

        const time_limit = shopConfig.planDetails?.usage_limits?.data_time_limit ?? "unlimited";
        if (time_limit === "unlimited") {
            return false;
        }
        if (time_limit === "3_months") {
            return start.isBefore(dayjs().subtract(3, "month").startOf("month"))
        }
        if (time_limit === "1_year") {
            return start.isBefore(dayjs().subtract(1, "year").startOf("month"))
        }
    }

    const setComparison = (p) => {
        if (p == "no-compare") {
            setPrevOpen(false)
            setSelectedFilters(dispatch, {
                compare : false
            })
        } else {
            setSelectedFilters(dispatch, {
                compare : true
            })
        }
    }

    const handlePrevDateChange = (dates, dateStrings) => {

        let start = dayjs(dateStrings[0]);
        let end = dayjs(dateStrings[1]);

        // Validate date range
        if (checkIfPremiumRequired(start) || checkIfPremiumRequired(end)) {
            tracker.event("Paywall", {feature: 'data_time_limit', report: (props.report ?? "unknown")})
            NiceModal.show(PaywallDialog, {feature : "data_time_limit"})
            return;
        }

        setSelectedFilters(dispatch, {
            compare : true,
            compare_start_date: start.toDate(),
            compare_end_date: end.toDate()
        })
    }

    let comparePresets = [
        {
            label : "no-compare",
            value: () => {
                return [null, null]
            }
        },
        {
            label : "prev-period",
            value: () => {
                let days_diff = dayjs(end_date).diff(dayjs(start_date), "day") + 1
                let start = dayjs(start_date).subtract(days_diff, "day")
                let end = dayjs(end_date).subtract(days_diff, "day")
                return [start, end]
            }
        },
        {
            label : "prev-year",
            value: () => {
                return [
                    dayjs(start_date).subtract(1, "year"),
                    dayjs(end_date).subtract(1, "year"),
                ]
            }
        }
    ];

    comparePresets = comparePresets.map((preset) => {
        let ifPremium = checkIfPremiumRequired(preset.value[0])
        return {
            ...preset,
            label: <MDTypography fontWeight="regular" variant="button" color="dark" px={1} onClick={() => {
                    !ifPremium && setComparison(preset.label)
                }}>
                {ifPremium
                    && <MDBox component="img" src={premiumTag} alt="premium" width="1rem" mr={0.8} mb={-0.3}/>
                }
                {t(preset.label)}
            </MDTypography>
        }
    })


    return (
        <Space>
        <MDTooltip title={t("compare-period-tooltip")} arrow placement={"top"}>
        <MDBox display="flex" alignItems="center" justifyContent="center" pl={1}>
                <Icon color="warning" sx={{cursor: "pointer"}} onClick={() => setPrevOpen(true)}>stacked_line_chart</Icon>
                <RangePicker
                    inputReadOnly
                    variant="borderless"
                    style={(!compare && !prevOpen) ? {
                        "visibility": "hidden",
                        "height" : 0,
                        "padding": 0,
                        "width": 0,
                        "position": "absolute",
                    } : {}}
                    open={prevOpen}
                    disableDatesAfter={dayjs().add(1, "day").toDate()}
                    value={[dayjs(compare_start_date), dayjs(compare_end_date)]}
                    format={dateFormat}
                    allowClear={false}
                    disabledDate={(current) => {
                        // Can not select days before today and today
                        return (
                            current && current.toDate() > dayjs(start_date).subtract(1, "day").toDate()
                        );
                    }}
                    size={"middle"}
                    onChange={handlePrevDateChange}
                    suffixIcon={false}
                    presets={comparePresets}
                    onClick={() => {
                        setPrevOpen(true)
                    }}
                    onOpenChange={(open) => {
                        setPrevOpen(open)
                    }}
                />
                {!compare && !prevOpen && <MDTypography className="input-label-text" variant="caption" color="secondary" fontWeight="regular" mr={0.5} ml={0.5} onClick={() => setPrevOpen(true)} style={{padding: "13px 4px"}}>
                    {t("no-compare")}
                </MDTypography>}
        </MDBox>
        </MDTooltip>
        </Space>
    );
}

export default function DatePickerRelative(props) {
    const [controller, dispatch] = useMaterialUIController();
    const [open, setOpen] = useState(false);
    const {selectedFilters, shopConfig} = controller;
    const {start_date, end_date} = selectedFilters;
    const {t} = useTranslation();

    const { RangePicker } = DatePicker;
    const dateFormat = "D MMM YYYY";

    const checkIfPremiumRequired = (start) => {
        let isSubscriptionEnabled = (shopConfig.subscription_enabled ?? false) 
        if (!start || !isSubscriptionEnabled) {
            return false;
        }
        const time_limit = shopConfig.planDetails?.usage_limits?.data_time_limit ?? "unlimited";
        
        if (time_limit === "unlimited") {
            return false;
        }
        if (time_limit === "3_months") {
            return start.isBefore(dayjs().subtract(3, "month").startOf("month"))
        }
        if (time_limit === "1_year") {
            return start.isBefore(dayjs().subtract(1, "year").startOf("month"))
        }
    }

    useEffect(() => {
        if (checkIfPremiumRequired(dayjs(start_date))) {
            // Reset dates to initial values
            setSelectedFilters(dispatch, {
                start_date: dayjs().subtract(3, "month").startOf("month").toDate(),
                end_date: dayjs().toDate(),
                compare_start_date: dayjs().subtract(7, "month").startOf("month").toDate(),
                compare_end_date: dayjs().subtract(4, "month").toDate()
            });

            // Open the date picker
            setOpen(true);
        }

    }, [shopConfig.subscription_enabled, shopConfig.planDetails?.usage_limits?.data_time_limit]);

    let defaultPresets = [
        {
            label : "last-7d",
            value: [
                dayjs().subtract(7, "day"),
                dayjs(),
            ]
        },
        {
            label : "last-30d",
            value: [
                dayjs().subtract(30, "day"),
                dayjs(),
            ]
        },
        {
            label : "wtd",
            value: [
                dayjs().startOf("week"),
                dayjs(),
            ]
        },
        {
            label : "qtd",
            value: [
                dayjs().startOf("quarter"),
                dayjs(),
            ]
        },
        {
            label : "ytd",
            value: [
                dayjs().startOf("year"),
                dayjs(),
            ]
        },
        {
            label : "last-3m",
            value: [
                dayjs().subtract(3, "month").startOf("month"),
                dayjs(),
            ]
        },
        {
            label : "last-6m",
            value: [
                dayjs().subtract(6, "month").startOf("month"),
                dayjs(),
            ]
        },
        {
            label : "last-1y",
            value: [
                dayjs().subtract(12, "month").startOf("month"),
                dayjs(),
            ]
        },
        {
            label : "last-2y",
            value: [
                dayjs().subtract(24, "month").startOf("month"),
                dayjs(),
            ]
        },
        {
            label : "last-3y",
            value: [
                dayjs().subtract(36, "month").startOf("month"),
                dayjs(),
            ]
        },
        {
            label : "last-5y",
            value: [
                dayjs().subtract(60, "month").startOf("month"),
                dayjs(),
            ]
        }
        // {
        //     label : "BFCM 2023 🛍️",
        //     value : [
        //         dayjs("2023-11-24"),
        //         dayjs("2023-11-27"),
        //     ]
        // },
        // {
        //     label : "BFCM 2022 🛍️",
        //     value: [
        //         dayjs("2022-11-25"),
        //         dayjs("2022-11-28"),
        //     ]
        // }
    ]


    const handleDateChange = (dates, dateStrings) => {
        let start = dayjs(dateStrings[0]);
        let end = dayjs(dateStrings[1]);

        // Validate date range
        if (checkIfPremiumRequired(start) || checkIfPremiumRequired(end)) {
            tracker.event("Paywall", {feature: 'data_time_limit', report: (props.report ?? "unknown")})
            NiceModal.show(PaywallDialog, {feature : "data_time_limit"})
            return;
        }

        // Setting Prev dates based on current dates
        let days_diff = end.diff(start, "day") + 1
        setSelectedFilters(dispatch, {
            start_date: start.toDate(),
            end_date: end.toDate(),

            // // TODO - compare as per selected preset
            compare_start_date: start.subtract(days_diff, "day").toDate(),
            compare_end_date: end.subtract(days_diff, "day").toDate(),
        })
    }

    let presets = defaultPresets.map((preset) => {

        let ifPremium = checkIfPremiumRequired(preset.value[0])
        return {
            ...preset,
            label: <MDTypography fontWeight="regular" variant="button" color="dark" px={1}>
                {ifPremium 
                    && <MDBox component="img" src={premiumTag} alt="premium" width="1rem" mr={0.8} mb={-0.3}/>
                }
                {t(preset.label)}
            </MDTypography>
        }
    })

    let date_preset = useMemo(() => {
        let tempPreset = "";

        if (!start_date || !end_date) {
            return tempPreset
        }

        let start = dayjs(start_date)
        let end = dayjs(end_date)
        for (let i = 0; i < defaultPresets.length; i++) {
            if (start.isSame(defaultPresets[i].value[0], "day") && end.isSame(defaultPresets[i].value[1], "day")) {
                tempPreset = defaultPresets[i].label
                break;
            }
        }

        return tempPreset
    }, [start_date, end_date]);

    return (
        <div style={{ display: "flex", alignItems: "baseline" }}>
            <MDBox mr={2}>
            <Space>
                <MDBox display="flex" alignItems="center" justifyContent="center" pl={1}>
                <MDBox className={date_preset ? "date-picker-preset" : ""} display="flex" alignItems="center" justifyContent="center" bgColor={date_preset ? "white" : ""} pl={date_preset ? 1 : 0} borderRadius="md">
                <Icon color="secondary" sx={{cursor: "pointer"}} onClick={() => setOpen(true)}>edit_calendar</Icon>
                    {date_preset && <MDTypography className="input-label-text" variant="caption" color="secondary" fontWeight="regular" mr={0.5} ml={0.5} onClick={() => setOpen(true)}>
                        {t(date_preset)}
                    </MDTypography>}
                </MDBox>
                <RangePicker
                    inputReadOnly
                    variant="borderless"
                    open={open}
                    onClick={() => {
                        setOpen(true)
                    }}
                    onOpenChange={(open) => {
                        setOpen(open)
                    }}
                    disableDatesAfter={dayjs().add(1, "day").toDate()}
                    value={[dayjs(start_date), dayjs(end_date)]}
                    format={dateFormat}
                    allowClear={false}
                    disabledDate={(current) => {
                        // Can not select days before today and today
                        return (
                            current && current.toDate() > dayjs().add(1, "day").toDate()
                        );
                    }}
                    size={"middle"}
                    onChange={handleDateChange}
                    presets={presets}
                    suffixIcon={false}
                    // onOk={(a) => console.log}
                />
                </MDBox>
            </Space>
            </MDBox>
            {!!props.enableComparison && <ComparisonDatePicker report={props.report ?? "unknown"} />}
        </div>
    );
}