import React, {useState, useEffect, useRef} from "react";
// antd imports
import {Form, Spin, Select, Input} from "antd";
import Drawer from "@mui/material/Drawer";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDTooltip from "@/components/MDTooltip";
import Divider from "@mui/material/Divider";
import Icon from "@mui/material/Icon";
import MDButton from "@/components/MDButton";
import { useTranslation } from "react-i18next";
import { cubejsApi } from "@/context";
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { styled } from '@mui/material/styles';
import ArrowForwardIosSharpIcon from '@mui/icons-material/ArrowForwardIosSharp';
import MuiAccordion from '@mui/material/Accordion';
import MuiAccordionSummary from '@mui/material/AccordionSummary';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {tracker, useMaterialUIController, setSelectedFilters} from "@/context";
import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import premiumTag from "@/assets/images/premium-tag.png";


// const defaultFilters = {
//     order_tags : [],
//     customer_tags : [],
//     product_title : [],
//     product_tags : [],
//     product_type : [],
//     source_name : [],
//     countries : [],
//     states : [],
//     cities : [],
//     product_vendor : []
// }


const labelDropdownFilterOperators = [
    'op_is',
    'op_is_not'
];

const labelFilterOperators = [
    "op_is",
    "op_is_not",
    "op_contains",
    "op_contains_not"
    // "op_starts",
    // "op_ends"
    // "op_any",
    // "op_any_not"
];

const filterAccordions = [
    {
        title: "order",
        sections: [
            {
                tooltip : "order-tag-tooltip",
                filter: "order_tags",
            },
            {
                tooltip : "filter-section-tooltip",
                filter: "source_name",
            }
        ]
    },
    {
        title: "customer",
        sections: [
            {
                tooltip : "order-tag-tooltip",
                filter: "customer_tags",
            }
        ]
    },
    {
        title: "product",
        sections: [
            {
                tooltip : "product-title-tooltip",
                filter: "product_title",
            },
            {
                tooltip : "order-tag-tooltip",
                filter: "product_tags",
            },
            {
                tooltip : "order-type-tooltip",
                filter: "product_type",
            },
            {
                tooltip : "filter-section-tooltip",
                filter: "product_vendor",
            }
        ]
    },
    {
        title: "location",
        sections: [
            {
                tooltip : "countries-tooltip",
                filter: "shipping_address_country",
            },
            {
                tooltip : "states-tooltip",
                filter: "shipping_address_province",
            },
            {
                tooltip : "cities-tooltip",
                filter: "shipping_address_city",
            }
        ]
    }
];

const Accordion = styled((props) => (
    <MuiAccordion disableGutters elevation={0} square {...props} />
  ))(({ theme }) => ({
    // border: `1px solid ${theme.palette.divider}`,
    border: "0px",
    marginBottom : theme.spacing(1),
}));

const AccordionSummary = styled((props) => (
    <MuiAccordionSummary
      expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: '0.9rem' }} />}
      sx={{
            '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)', // Change this to your desired color
            },
        }}
      {...props}
    />
  ))(({ theme }) => ({
    flexDirection: 'row-reverse',
    // border: `1px solid ${theme.palette.divider}`,
    '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
      transform: 'rotate(90deg)',
    },
    '& .MuiAccordionSummary-content': {
      marginLeft: theme.spacing(1),
    },
}));

const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
    paddingLeft: theme.spacing(1.5),
    paddingRight: theme.spacing(1.5),
    paddingTop: theme.spacing(1),
    paddingBottom: theme.spacing(1),
    borderTop: '1px solid rgba(0, 0, 0, .125)',
    border: '0px',
    // backgroundColor: 'rgba(0, 0, 0, .03)'
}));

let filterQueries = {
    'shipping_address_city' : {
        "dimensions": [
            "Orders.shippingAddressCity"
        ],
        "filters": [
            {
              "member": "Orders.shippingAddressCity",
              "operator": "set"
            }
        ],
        "limit": 5000
    },
    'shipping_address_province' : {
        "dimensions": [
            "Orders.shippingAddressProvince"
        ],
        "filters": [
            {
              "member": "Orders.shippingAddressProvince",
              "operator": "set"
            }
        ],
        "limit": 5000
    },
    'shipping_address_country' : {
        "dimensions": [
            "Orders.shippingAddressCountry"
        ],
        "filters": [
            {
              "member": "Orders.shippingAddressCountry",
              "operator": "set"
            }
        ],
        "limit": 5000
    },
    'order_tags' : {
        "dimensions": [
          "Orders.tags"
        ],
        "filters": [
          {
            "member": "Orders.tags",
            "operator": "set"
          }
        ],
        "limit": 5000
    },
    'customer_tags' : {
        "dimensions": [
          "Customers.tags"
        ],
        "filters": [
          {
            "member": "Customers.tags",
            "operator": "set"
          }
        ],
        "limit": 5000
    },
    'source_name' : {
        "dimensions": [
          "Orders.sourceName"
        ],
        "filters": [
          {
            "member": "Orders.sourceName",
            "operator": "set"
          }
        ],
        "limit": 5000
    },
    'product_type' : {
        "dimensions": [
          "Products.productType"
        ],
        "filters": [
          {
            "member": "Products.productType",
            "operator": "set"
          }
        ],
        "limit": 5000
    },
    'product_tags' : {
        "dimensions": [
          "Products.tags"
        ],
        "filters": [
          {
            "member": "Products.tags",
            "operator": "set"
          }
        ],
        "limit": 5000
    },
    'product_vendor' : {
        "dimensions": [
          "Products.vendor"
        ],
        "filters": [
          {
            "member": "Products.vendor",
            "operator": "set"
          }
        ],
        "limit": 5000
    },
    'product_title' : {
        "dimensions": [
            "Products.title"
        ],
        "filters": [
            {
                "member": "Products.title",
                "operator": "set"
            }
        ],
        "limit": 5000
    }
    // 'products' : (query) => {
    //     return {
    //         "dimensions": [
    //             "Products.productId",
    //             "Products.title"
    //         ],
    //         "filters": [
    //             {
    //                 "member": "Products.title",
    //                 "operator": "startsWith",
    //                 "values": [query]
    //             }
    //         ],
    //         "limit": 100
    //     }
    // }
}


let filterQueriesOSS = {
    'shipping_address_city' : {
        "dimensions": [
            "ShopifyOrders.shipping_address_city"
        ],
        "filters": [
            {
              "member": "ShopifyOrders.shipping_address_city",
              "operator": "set"
            }
        ],
        "limit": 5000
    },
    'shipping_address_province' : {
        "dimensions": [
            "ShopifyOrders.shipping_address_province"
        ],
        "filters": [
            {
              "member": "ShopifyOrders.shipping_address_province",
              "operator": "set"
            }
        ],
        "limit": 5000
    },
    'shipping_address_country' : {
        "dimensions": [
            "ShopifyOrders.shipping_address_country"
        ],
        "filters": [
            {
              "member": "ShopifyOrders.shipping_address_country",
              "operator": "set"
            }
        ],
        "limit": 5000
    },
    'order_tags' : {
        "dimensions": [
          "ShopifyOrders.tags"
        ],
        "filters": [
          {
            "member": "ShopifyOrders.tags",
            "operator": "set"
          }
        ],
        "limit": 5000
    },
    'customer_tags' : {
        "dimensions": [
          "ShopifyCustomers.tags"
        ],
        "filters": [
          {
            "member": "ShopifyCustomers.tags",
            "operator": "set"
          }
        ],
        "limit": 5000
    },
    'source_name' : {
        "dimensions": [
          "ShopifyOrders.source_name"
        ],
        "filters": [
          {
            "member": "ShopifyOrders.source_name",
            "operator": "set"
          }
        ],
        "limit": 5000
    },
    'product_type' : {
        "dimensions": [
          "ShopifyProducts.product_type"
        ],
        "filters": [
          {
            "member": "ShopifyProducts.product_type",
            "operator": "set"
          }
        ],
        "limit": 5000
    },
    'product_tags' : {
        "dimensions": [
          "ShopifyProducts.tags"
        ],
        "filters": [
          {
            "member": "ShopifyProducts.tags",
            "operator": "set"
          }
        ],
        "limit": 5000
    },
    'product_vendor' : {
        "dimensions": [
          "ShopifyProducts.vendor"
        ],
        "filters": [
          {
            "member": "ShopifyProducts.vendor",
            "operator": "set"
          }
        ],
        "limit": 5000
    },
    'product_title' : {
        "dimensions": [
            "ShopifyProducts.title"
        ],
        "filters": [
            {
                "member": "ShopifyProducts.title",
                "operator": "set"
            }
        ],
        "limit": 5000
    }
    // 'products' : (query) => {
    //     return {
    //         "dimensions": [
    //             "Products.productId",
    //             "Products.title"
    //         ],
    //         "filters": [
    //             {
    //                 "member": "Products.title",
    //                 "operator": "startsWith",
    //                 "values": [query]
    //             }
    //         ],
    //         "limit": 100
    //     }
    // }
}

function processTags(resultSet) {

    let tags = []
    for (var k in resultSet) {
        for (var ki in resultSet[k]) {
            tags.push(resultSet[k][ki].toString())
        }
    }

    var processed_tags = {}

    for (var k in tags) {
        if (!tags[k] || tags[k] == "") {
            continue
        }

        let tagStr = tags[k].trim();
        if (!tagStr) {
            continue
        }

        let tagList = tagStr.split(",")
        for (var ti in tagList) {
            tagList[ti] = tagList[ti].trim()
            if (!tagList[ti]) {
                continue
            }

            if (!(tagList[ti] in processed_tags)) {
                processed_tags[tagList[ti]] = 1
            }
        }
    }
    
    return Object.keys(processed_tags)
}

function FilterSection({filter, tooltip, options, setOptions, sectionRules, handleFilterUpdate}) {

    const {t} = useTranslation();
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, shopConfig} = controller;

    const [optionsLoading, setOptionsLoading] = useState(false);
    const [optionsFetched, setOptionsFetched] = useState(false);

    const useMigrationDataset = shopConfig.useMigrationDataset ?? false;
    const filterQueriesToUse = useMigrationDataset ? filterQueriesOSS : filterQueries;

    const getFilterOptions = () => {

        if (!(filter in filterQueriesToUse)) {
            console.log('Invalid filter type');
            return
        }

        if (optionsFetched || options.length > 0) {
            return
        }

        setOptionsLoading(true)
        let cubeApi = cubejsApi(selectedShop, useMigrationDataset ? "ShopifyShops" : "Shops");
        cubeApi.load(filterQueriesToUse[filter]).then(function (resultSet) {
            let result = processTags(resultSet.tablePivot())
            setOptions(result)
            setOptionsFetched(true)
            setOptionsLoading(false)
        }).catch((err) => {
            console.log("error", err);
            setOptionsLoading(false)
            // not setting optionsFetched to true - can query again if there was an error
        })
    }

    const addSectionRule = () => {
        handleFilterUpdate(filter, 'add', {
            id : Math.random().toString(36).substring(7),
            op : "op_is",
            values : []
        })
    }

    const updateSectionRuleOperator = (id, prevOp, op) => {
        handleFilterUpdate(filter, 'update', {
            id : id,
            op : op,
            values : []
        })
    }

    const removeSectionRule = (id) => {
        handleFilterUpdate(filter, 'remove', {
            id : id
        })
    }

    const updateSectionRuleValues = (id, vals) => {
        handleFilterUpdate(filter, 'update', {
            id : id,
            values : vals
        })
    }

    return (
        <MDBox borderRadius="md" sx={{border: "1px solid rgba(0, 0, 0, .125)"}} mb={2}>
            <MDBox borderRadius="md" p={0.5} bgColor="grey-100" display="flex" alignItems="center" justifyContent="space-between"  width="100%">
                <MDBox display="flex" alignItems="center" justifyContent="center" pl={1}>
                    <MDTypography variant="caption" fontWeight="medium" color="dark" mr={1}>
                        {t(filter)}
                    </MDTypography>
                    <MDTooltip title={t(tooltip)} placement="top" >
                        <InfoOutlinedIcon />
                    </MDTooltip>
                </MDBox>
                <MDBox display="flex" justifyContent="left" alignItems="left">
                    <MDButton variant="text" color="info" fontWeight="light" size="small" onClick={addSectionRule}>
                        <Icon fontSize="medium">add</Icon>
                        &nbsp;{t("add")}
                    </MDButton>
                </MDBox>
            </MDBox>

            {sectionRules.map((rule, index) => (<Form.Item
                key={rule.id}
                label={null}
                style={{ marginBottom: "0px" }}
                colon={false}
            >
                <MDBox key={rule.id} borderRadius="md">
                    <MDBox width="100%" sx={{border: "1px solid rgba(0, 0, 0, .125)", borderLeft: "0", borderRight:"0"}}>
                        <MDBox display="flex" alignItems="center" justifyContent="center">
                            <Select
                                className="no-border-radius no-focus-color no-border-all"
                                style={{ width: '100%'}}
                                value={rule.op}
                                dropdownMatchSelectWidth={false}
                                dropdownStyle={{ width: 'auto' }}
                                options={labelFilterOperators.map((o) => ({label: t(o), value: o}))}
                                getPopupContainer={() => document.getElementById("filter-form")}
                                onChange={(val) => updateSectionRuleOperator(rule.id, rule.op, val)}
                            />
                            <MDBox sx={{borderLeft: "1px solid rgba(0, 0, 0, .125)", cursor:"pointer", fontSize:"18px !important"}} width="10%" display="flex" alignItems="center" justifyContent="center" onClick={() => removeSectionRule(rule.id)}>
                                <Icon color="secondary" fontSize="14px" sx={{fontSize:"18px !important"}}>cancel</Icon>
                            </MDBox>
                        </MDBox>
                    </MDBox>
                    <MDBox width="100%">
                        {labelDropdownFilterOperators.includes(rule.op)
                            ? <Select
                                mode="multiple"
                                className="no-focus-color no-border-all"
                                allowClear
                                variant="borderless"
                                style={{ width: "100%", borderRadius: "0px"}}
                                placeholder={t("f-placeholder")}
                                value={rule.values}
                                notFoundContent={optionsLoading ? <Spin size="small" /> : null}
                                options={options
                                        .filter((tg) => !rule.values.includes(tg))
                                        .map(tg => ({label : tg, value : tg}))}
                                onChange={(vals) => updateSectionRuleValues(rule.id, vals)}
                                getPopupContainer={() => document.getElementById("filter-form")}
                                onDropdownVisibleChange={getFilterOptions}
                                />
                            : <Input
                                placeholder={t("f-placeholder")}
                                style={{ border: 'none', outline: 'none', boxShadow: 'none' }}
                                value={rule.values}
                                onChange={(e) => updateSectionRuleValues(rule.id, [e.target.value])} />
                        }
                        </MDBox>
                </MDBox>
            </Form.Item>)
            )}
        </MDBox>
    );
}

export default function FilterDrawer({feature}) {
    const {t} = useTranslation();
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, selectedFilters, shopConfig} = controller;
    const {applied_filters, filter_version} = selectedFilters;

    let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
    let blockApplyFilters = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.[feature] ?? false);

    const filterCount = Object.values(applied_filters).filter(f => f.length != 0).length;
  
    const [draftFilters, setDraftFilters] = useState({});
    const [showFilters, setShowFilters] = useState(false);
    const [options, setOptions] = useState({});

    const resetFilters = () => {
        // not resetting options
        setDraftFilters({});
        setShowFilters(false)
        setSelectedFilters(dispatch, {
            applied_filters : {},
            filter_version : 1
        })
    };

    const initialRender = useRef(true);
    useEffect(() => {
        if (initialRender.current) {
            // Skip resetting filters on initial render
            initialRender.current = false;
        } else {
            setOptions({});
            // Reset filters when selectedShop changes
            resetFilters();
        }
    }, [selectedShop]);

    useEffect(() => {
        if (filterCount) {
            setDraftFilters(applied_filters);
        }
    }, [])

    const handleFilterUpdate = (filter, action, sectionRule) => {
        console.log(filter, action, sectionRule)
        setDraftFilters(prevFilters => {
            let newFilters = { ...prevFilters };
            if (!(filter in newFilters)) {
                newFilters[filter] = [];
            }
            switch (action) {
                case 'add':
                    newFilters[filter].push(sectionRule);
                    break;
                case 'update':
                    let ruleIndex = newFilters[filter].findIndex(rule => rule.id === sectionRule.id);
                    if (ruleIndex !== -1) {
                        newFilters[filter][ruleIndex] = { ...newFilters[filter][ruleIndex], ...sectionRule };
                    }
                    break;
                case 'remove':
                    newFilters[filter] = newFilters[filter].filter(rule => rule.id !== sectionRule.id);
                    break;
                default:
                    console.log('Invalid action');
            }
            return newFilters;
        });
    };

    const applyFilters = () => {
        if (blockApplyFilters) {
            tracker.event("Paywall", {feature: "cohort_filters"});
            NiceModal.show(PaywallDialog, {feature : "cohort_filters"})
            return false
        }
        setShowFilters(false)
        setSelectedFilters(dispatch, {
            applied_filters : (draftFilters),
            filter_version : filter_version + 1
        })
    }

    const filterToggle = () => setShowFilters(!showFilters)

    return (
        <MDBox>
            <MDButton variant="outlined" color={filterCount > 0 ? "info" : "dark"} size="medium" onClick={filterToggle} >
                <Icon>tune</Icon>
                &nbsp;{t("filters")} {filterCount > 0 ? `(${filterCount})` : ''}
            </MDButton>
            <Drawer
                anchor={"right"}
                open={showFilters}
                onClose={filterToggle}
                width={"100%"}
                sx={{
                    "& .MuiDrawer-paper": {
                        width: "100%",
                        maxWidth: "400px"
                    },
                }}
            >
            <MDBox m={3} >
                <MDBox
                    display="flex"
                    justifyContent="space-between"
                    alignItems="baseline"
                >
                    <MDTypography variant="h5">{t("filters")}</MDTypography>
                    <Icon
                        sx={({ typography: { size }, palette: { dark, white } }) => ({
                            fontSize: `${size.lg} !important`,
                            color: dark.main,
                            stroke: "currentColor",
                            strokeWidth: "2px",
                            cursor: "pointer",
                            transform: "translateY(5px)",
                        })}
                        onClick={filterToggle}
                        >
                        close
                    </Icon>
                </MDBox>

                <Divider  style={{margin:"10px"}} />

                <Form
                    layout="vertical"
                    id="filter-form"
                    style={{ position: "relative" }}
                >
                    {filterAccordions.map((a, index) => {
                        let appliedCount = 0;
                        if (a.sections) {
                            a.sections.forEach(s => {
                                if (!!draftFilters[s.filter] && draftFilters[s.filter].length > 0) {
                                    appliedCount += 1;
                                }
                            });
                        }

                        return (
                            <MDBox key={index} mb={1}>
                                <Accordion>
                                    <AccordionSummary
                                        aria-controls="panel1a-content"
                                        id="panel1a-header"
                                    >
                                        <MDTypography
                                            variant="button"
                                            fontWeight="medium"
                                            textTransform="uppercase"
                                            color={appliedCount > 0 ? "info" : "secondary"}
                                        >
                                            {t(a.title)}&nbsp;
                                            {appliedCount > 0 ? <span style={{fontSize:"11px"}}>{`(${appliedCount})`}</span> : null}
                                        </MDTypography>
                                    </AccordionSummary>
                                    <AccordionDetails>
                                        {!!a.sections && a.sections.length > 0 && a.sections.map((s, ind) => 
                                            <FilterSection
                                                key={ind}
                                                filter={s.filter}
                                                tooltip={s.tooltip}
                                                options={options[s.filter] ?? []}
                                                setOptions={(vals) => setOptions(prevOptions => {
                                                    let newOptions = {...prevOptions}
                                                    newOptions[s.filter] = vals
                                                    return newOptions
                                                })}
                                                sectionRules={draftFilters[s.filter] ?? []}
                                                handleFilterUpdate={handleFilterUpdate}
                                            />
                                        )}
                                    </AccordionDetails>
                                </Accordion>
                            </MDBox>
                        )
                    })}
                </Form>

                <MDBox
                    display="flex"
                    justifyContent="space-between"
                    mt={3}
                >
                    <MDButton onClick={resetFilters} color="secondary" variant="text" size="small" px={2}>
                        {t("reset")}
                    </MDButton>
                    <MDButton onClick={applyFilters} variant="gradient" color="info" size="small" px={2} fullWidth>
                        {t("apply-filters")}
                        {blockApplyFilters && <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={1} />}
                    </MDButton>
                </MDBox>
                </MDBox>
            </Drawer>
        </MDBox>
    );
}