import { useState, useEffect } from "react";

// react-router components
import { useLocation, Link } from "react-router-dom";
import {tracker} from "@/context";
// prop-types is a library for typechecking of props.
import PropTypes from "prop-types";
import SectionNavigation from "@/examples/Navbars/SectionNavigation";

// @material-ui core components
import AppBar from "@mui/material/AppBar";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import Menu from "@mui/material/Menu";
import Icon from "@mui/material/Icon";
import MUILink from "@mui/material/Link";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTooltip from "@/components/MDTooltip";

// Material Dashboard 2 PRO React examples
import Breadcrumbs from "@/examples/Breadcrumbs";
import NotificationItem from "@/examples/Items/NotificationItem";
import { ConsolidatedViewDropdown } from "@/layouts/pages/profile/current";

// Custom styles for DashboardNavbar
import {
  navbar,
  navbarContainer,
  navbarRow,
  navbarIconButton,
  navbarDesktopMenu,
  navbarMobileMenu,
} from "@/examples/Navbars/DashboardNavbar/styles";

// Material Dashboard 2 PRO React context
import {
  useMaterialUIController,
  setTransparentNavbar,
  setMiniSidenav,
  setOpenConfigurator,
} from "@/context";

import { useTranslation } from 'react-i18next';

function DashboardNavbar({ absolute, light, isMini, sections }) {
  const [navbarType, setNavbarType] = useState();
  const [controller, dispatch] = useMaterialUIController();
  const { miniSidenav, transparentNavbar, fixedNavbar, openConfigurator, darkMode, loginConfig, shopConfig} = controller;
  const [openMenu, setOpenMenu] = useState(false);
  const route = useLocation().pathname.split("/").slice(1);
  const { i18n, t } = useTranslation();
  const { pathname } = useLocation();


  let showSubscriptionBanner = shopConfig.subscription_enabled
  && shopConfig.planDetails?.planType == "free";

  useEffect(() => {
    // Setting the navbar type
    if (fixedNavbar) {
      setNavbarType("sticky");
    } else {
      setNavbarType("static");
    }

    // A function that sets the transparent state of the navbar.
    function handleTransparentNavbar() {
      setTransparentNavbar(dispatch, (fixedNavbar && window.scrollY === 0) || !fixedNavbar);
    }

    /** 
     The event listener that's calling the handleTransparentNavbar function when 
     scrolling the window.
    */
    window.addEventListener("scroll", handleTransparentNavbar);

    // Call the handleTransparentNavbar function to set the state with the initial value.
    handleTransparentNavbar();

    // Remove event listener on cleanup
    return () => window.removeEventListener("scroll", handleTransparentNavbar);
  }, [dispatch, fixedNavbar]);

  const handleMiniSidenav = () => setMiniSidenav(dispatch, !miniSidenav);
  const handleConfiguratorOpen = () => setOpenConfigurator(dispatch, !openConfigurator);
  const handleOpenMenu = (event) => setOpenMenu(event.currentTarget);
  const handleCloseMenu = () => setOpenMenu(false);

  // Render the notifications menu
  const renderMenu = () => (
    <Menu
      anchorEl={openMenu}
      anchorReference={null}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      open={Boolean(openMenu)}
      onClose={handleCloseMenu}
      sx={{ mt: 2 }}
    >
      <NotificationItem title="English" onClick={() => {i18n.changeLanguage('en'); handleCloseMenu()}}/>
      <NotificationItem title="Español" onClick={() => {i18n.changeLanguage('es'); handleCloseMenu()}}/>
      <NotificationItem title="Français" onClick={() => {i18n.changeLanguage('fr'); handleCloseMenu()}}/>
      <NotificationItem title="Deutsch" onClick={() => {i18n.changeLanguage('de'); handleCloseMenu()}}/>
    </Menu>
  );

  // Styles for the navbar icons
  const iconsStyle = ({ palette: { dark, white, text }, functions: { rgba } }) => ({
    color: () => {
      let colorValue = light || darkMode ? white.main : dark.main;

      if (transparentNavbar && !light) {
        colorValue = darkMode ? rgba(text.main, 0.6) : text.main;
      }

      return colorValue;
    },
  });

  return (
    <AppBar
      position={absolute ? "absolute" : navbarType}
      color="inherit"
      sx={(theme) => navbar(theme, { transparentNavbar, absolute, light, darkMode, showSubscriptionBanner })}
    >
      <Toolbar sx={(theme) => navbarContainer(theme)}>
        <MDBox color="inherit" mb={{ xs: 1, md: 0 }} sx={(theme) => navbarRow(theme, { isMini })}>
          <Breadcrumbs icon="home" title={route[route.length - 1].replace("-", " ")} route={route} light={light} />
          <IconButton sx={navbarDesktopMenu} onClick={handleMiniSidenav} size="small" disableRipple>
            <Icon fontSize="medium" sx={iconsStyle}>
              {miniSidenav ? "menu_open" : "menu"}
            </Icon>
          </IconButton>
        </MDBox>
        {isMini ? null : (
          <MDBox sx={(theme) => navbarRow(theme, { isMini })}>
            {sections && sections.length > 0 && <SectionNavigation sections={sections} light={light} />}
            <ConsolidatedViewDropdown light={light} />
            <MDBox color={light ? "white" : "inherit"}>
              {/* <Link to="/authentication/sign-in/basic">
                <IconButton sx={navbarIconButton} size="small" disableRipple>
                  <Icon sx={iconsStyle}>account_circle</Icon>
                </IconButton>
              </Link> */}
              <IconButton
                size="small"
                disableRipple
                color="inherit"
                sx={navbarMobileMenu}
                onClick={handleMiniSidenav}
              >
                <Icon sx={iconsStyle} fontSize="medium">
                  {miniSidenav ? "menu_open" : "menu"}
                </Icon>
              </IconButton>
                {loginConfig.admin && 
                  <IconButton
                    size="small"
                    disableRipple
                    color="inherit"
                    sx={navbarIconButton}
                    onClick={handleConfiguratorOpen}
                  >
                    <Icon sx={iconsStyle}>settings</Icon>
                  </IconButton>
                }

              <Link to="/book-call">
              <MDTooltip title={t("book-call")} onClick={() => {
                  tracker.event("Clicked BookCall", {page : pathname, source : "navbar"})
                }}>
                <IconButton
                  size="small"
                  disableRipple
                  color="inherit"
                  sx={navbarIconButton}
                  aria-controls="lang-menu"
                  aria-haspopup="true"
                  variant="contained"
                >
                  <Icon sx={iconsStyle}>event_available</Icon>
                </IconButton>
              </MDTooltip>
              </Link>

              <MUILink href="https://help.datadrew.io/en" target="_blank">
              <MDTooltip title={t("visit-help-center")} onClick={() => {
                  tracker.event("Clicked HelpCenter", {page : pathname, source : "navbar"})
                }}>
                <IconButton
                  size="small"
                  disableRipple
                  color="inherit"
                  sx={navbarIconButton}
                  aria-controls="lang-menu"
                  aria-haspopup="true"
                  variant="contained"
                >
                  <Icon sx={iconsStyle}>help_center_outlined</Icon>
                </IconButton>
              </MDTooltip>
              </MUILink>

              <MDTooltip title={t("translate")}>
              <IconButton
                size="small"
                disableRipple
                color="inherit"
                sx={navbarIconButton}
                aria-controls="lang-menu"
                aria-haspopup="true"
                variant="contained"
                onClick={handleOpenMenu}
              >
                <Icon sx={iconsStyle}>translate</Icon>
              </IconButton>
              </MDTooltip>
              {renderMenu()}
            </MDBox>
          </MDBox>
        )}
      </Toolbar>
    </AppBar>
  );
}

// Setting default values for the props of DashboardNavbar
DashboardNavbar.defaultProps = {
  absolute: false,
  light: false,
  isMini: false,
  sections: [],
};

// Typechecking props for the DashboardNavbar
DashboardNavbar.propTypes = {
  absolute: PropTypes.bool,
  light: PropTypes.bool,
  isMini: PropTypes.bool,
  sections: PropTypes.arrayOf(PropTypes.string),
};

export default DashboardNavbar;
