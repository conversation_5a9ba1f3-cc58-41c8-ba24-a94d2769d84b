import React, {useState, useEffect, useMemo} from "react";
// Material Dashboard 2 PRO React examples
import {Link} from "react-router-dom";
import MDTooltip from "@/components/MDTooltip";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
// Material Dashboard 2 PRO React examples
import {tracker, useMaterialUIController, useCancellableAxios} from "@/context";
import { useTranslation } from "react-i18next";
import logoFacebook from "@/assets/images/facebook-logo.png";
import logoGoogleAds from "@/assets/images/google-ads-logo.png";
import MDBadgeDot from "@/components/MDBadgeDot";
import {BLENDED, SOURCE_FB, SOURCE_GOOGLE_ADS} from "@/layouts/dashboards/metrics/metadata";

const ConnectPill = ({logo}) => {
    const {t} = useTranslation();
    return (
        <MDBox display="flex" justifyContent="center" alignItems="center" p={1} mr={1}
                    sx={{
                        cursor: "pointer",
                        borderRadius: ({ borders: { borderRadius } }) => borderRadius.md,
                        border: "1px solid #49a3f1"
                    }}
            component={Link} to="/integrations"
            >
            <MDBox component="img" src={logo} alt="ads" width="1rem" mr={0.5} />
            <MDTypography variant="caption" fontWeight="regular" color="text" textAlign="center" sx={{color:"#49a3f1"}}>
                {t("connect")}
            </MDTypography>
        </MDBox>
    )
}

const ConnectedPill = ({logo, accounts}) => {
    const {t} = useTranslation();

    const tooltipContent = (
        <MDBox display="flex" flexDirection="column" alignItems="left" justifyContent="center">
            {accounts.map((account) => {
                return (
                    <MDBadgeDot
                        key={account}
                        badgeContent={account}
                        color={"success"}
                        font={{color: "text", weight: "regular"}}
                        size="sm"/>
                );
            })}
        </MDBox>
    );

    return (
        <MDTooltip title={tooltipContent} placement="bottom">
        <MDBox display="flex" justifyContent="center" alignItems="center" sx={{cursor: "default"}} mr={1}>
            <MDBox component="img" src={logo} alt="ads" width="1rem" mr={0.5} />
            <MDTypography variant="caption" fontWeight="regular" color="text" textAlign="center">
                {t("connected")}
            </MDTypography>
        </MDBox>
        </MDTooltip>
    );
};



const ConnectedAdAccounts = ({preset}) => {
    
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, integrations} = controller;
    const {t} = useTranslation();

    const [ggAccounts, fbAccounts] = useMemo(() => {
        let fbIntgs = SOURCE_FB in integrations ? integrations[SOURCE_FB] : [];
        let ggIntgs = SOURCE_GOOGLE_ADS in integrations ? integrations[SOURCE_GOOGLE_ADS] : [];
        let fbAccounts = [];
        let ggAccounts = [];
        for (let intg of fbIntgs) {
            if (intg.synced) {
                const source_config_id = intg.source_config_id ?? "";
                const source_config_name = intg.source_config_name ?? "";
                let source_name = ""
                if (!!source_config_id && !!source_config_name) {
                    source_name = `${source_config_name}`;
                } else if (!!source_config_id) {
                    source_name = `#${source_config_id}`;
                } else {
                    source_name = t("integration.finish-account-setup");
                }
                fbAccounts.push(source_name);
            }
        }

        for (let intg of ggIntgs) {
            if (intg.synced) {
                const source_config_id = intg.source_config_id ?? "";
                const source_config_name = intg.source_config_name ?? "";
                let source_name = ""
                if (!!source_config_id && !!source_config_name) {
                    source_name = `${source_config_name}`;
                } else if (!!source_config_id) {
                    source_name = `#${source_config_id}`;
                } else {
                    source_name = t("integration.finish-account-setup");
                }
                ggAccounts.push(source_name);
            }
        }

        return [ggAccounts, fbAccounts];
    }, [integrations]);

    if (preset != BLENDED) {
        return null;
    }

    return (
        <MDBox display="flex" flexDirection="row" alignItems="center" justifyContent="center">
            {ggAccounts.length == 0 && <ConnectPill logo={logoGoogleAds} />}
            {fbAccounts.length == 0 && <ConnectPill logo={logoFacebook} />}
            {ggAccounts.length > 0 && <ConnectedPill logo={logoGoogleAds} accounts={ggAccounts} />}
            {fbAccounts.length > 0 && <ConnectedPill logo={logoFacebook} accounts={fbAccounts} />}
        </MDBox>
    );
}

export default ConnectedAdAccounts;