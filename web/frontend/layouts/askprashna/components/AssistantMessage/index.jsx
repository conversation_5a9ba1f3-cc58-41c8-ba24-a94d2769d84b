import { useState, useEffect } from "react";
import {
  useMessage,
  AssistantActionBar,
  MessagePrimitive,
} from "@assistant-ui/react";
import AssistantAvatar from "@/layouts/askprashna/components/AssistantAvatar/index.jsx";

import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import DataTable from "@/examples/Tables/DataTable";
import Card from "@mui/material/Card";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import { useTranslation } from "react-i18next";
import { StreamText } from "flowtoken";
import Plot from 'react-plotly.js';
import axios from 'axios';
import { useCancellableAxios } from "@/context/index.jsx";
import { useMaterialUIController } from "@/context/index.jsx";

const MyAssistantActionBar = () => {
  return (
    <AssistantActionBar.Root
      hideWhenRunning
      autohide="not-last"
      autohideFloat="single-branch"
    >
      <AssistantActionBar.SpeechControl />
      <AssistantActionBar.Copy />
      {/* <AssistantActionBar.Reload /> */}
      {/* <AssistantActionBar.FeedbackPositive /> */}
      {/* <AssistantActionBar.FeedbackNegative /> */}
    </AssistantActionBar.Root>
  );
};

const FinalAnswerText = (props) => {
  return (
    <div className="space-y-2 relative">
        <StreamText
          key="final-answer"
          content={props.text}
          windowSize={5}
          delayMultiplier={1.1}
          sep="word"
          animation="fadeIn"
          animationDuration="0.5s"
          animationTimingFunction="ease-in-out"
        />
    </div>
  );
};

const Steps = ({ steps, final_answer }) => {
    const [collapsed, setCollapsed] = useState(false);
    const {t} = useTranslation();
    useEffect(() => {
      if (final_answer) {
        setCollapsed(true);
      }
    }, [final_answer]);
  
    const toggleCollapse = () => {
      setCollapsed(!collapsed);
    };

    if (final_answer && steps.length <= 1) {
      return null;
    }

    return (
      <MDBox mt={1}>
        <div className="space-y-2 relative">
          {final_answer && (
            <button
            onClick={toggleCollapse}
            className="flex items-center underline text-sm mb-2 text-gray-500"
          >
            {collapsed ? <KeyboardArrowRightIcon fontSize="small" /> : <KeyboardArrowDownIcon fontSize="small" />}
            <span>{collapsed ? t("prashna.show-steps") : t("prashna.hide-steps")}</span>
          </button>
        )}
        {!collapsed && (
          (steps ?? []).map((step, index) => (
            <div 
              key={index} 
              className="flex items-start gap-2 text-sm text-gray-600 font-medium relative"
            >
              <div className="flex items-center gap-2 relative">
                {index < steps.length - 1 && (
                  <div className="absolute left-[7px] top-[16px] h-full w-[2px] bg-[#aecef7]"></div>
                )}
                <div className={`w-[16px] h-[16px] rounded-full flex items-center justify-center relative ${index === steps.length - 1 && !final_answer ? 'bg-[#8aaed7] animate-blink' : 'bg-[#8aaed7]'}`}>
                  <div className="w-[6px] h-[6px] rounded-full bg-[#1A73E8]" />
                </div>
                <span className="leading-relaxed">
                  <StreamText
                    key={step}
                    content={step}
                    windowSize={5}
                    delayMultiplier={1.1}
                    sep="word"
                    animation="fadeIn"
                    animationDuration="0.5s"
                    animationTimingFunction="ease-in-out"
                  />
                </span>
              </div>
            </div>
          ))
        )}
        </div>
        </MDBox>
    );
};

const PreviewData = ({ query_result }) => {
    const {t} = useTranslation();
    const [collapsed, setCollapsed] = useState(false);
    const columns = query_result?.columns ?? [];
    const data = query_result?.data ?? [];

    if (!columns || columns.length === 0 || !data || data.length === 0) {
        return null;
    }

    const previewData = {
        columns: columns.map((column) => ({
            Header: <MDTypography variant="button" fontWeight="medium">{column}</MDTypography>,
            accessor: column,
            Cell: ({ value }) => {
                return (
                    <MDTypography variant="button" fontWeight="regular">{value}</MDTypography>
                );
            }
        })),
        rows: data.map((row) => ({
            ...row,
        })),
    };

    const toggleCollapse = () => {
        setCollapsed(!collapsed);
    };

    return (
        <div className="space-y-2 relative w-full">
            <button
                onClick={toggleCollapse}
                className="flex items-center underline text-sm mb-2 text-gray-500"
            >
                {collapsed ? <KeyboardArrowRightIcon fontSize="small" /> : <KeyboardArrowDownIcon fontSize="small" />}
                <span>{collapsed ? t("prashna.preview-data") : t("prashna.hide-preview-data")}</span>
            </button>
            {!collapsed && (
                <MDBox sx={{
                    width: "100%",
                    mb: 1
                }} p={1}>
                    <Card>
                        <DataTable
                            isBasic={true}
                            table={previewData}
                            entriesPerPage={false}
                            showTotalEntries={false}
                            isSorted={false}
                            canSearch={false}
                        />
                    </Card>
                </MDBox>
            )}
        </div>
    );
};

const Visualization = ({ plotly_image, plotly_json_path }) => {
  const [plotlyJson, setPlotlyJson] = useState(null);
  const axiosInstance = useCancellableAxios();

  const [controller, dispatch] = useMaterialUIController();
  const {selectedShop, shopConfig, integrations} = controller;


  if (!plotly_image && !plotly_json_path) {
    return null;
  }
  

  useEffect(() => {
    const fetchPlotlyJson = async () => {

      let data = {plotly_json_path: plotly_json_path}
      if (selectedShop) {
        data.selectedShop = selectedShop;
      }

      try {
        const response = await axiosInstance.post(`/api/agent/plotly`, data);
        setPlotlyJson(response.data);
      } catch (error) {
        console.error("Error parsing plotly_json_path:", error);
        return null;
      }
    };

    fetchPlotlyJson();
  }, [plotly_json_path]);

  return (
    <MDBox mt={2}>
      {plotlyJson ? (
        <Plot
          data={plotlyJson.data}
          layout={plotlyJson.layout}
          config={{ responsive: true }}
        />
      ) : (plotly_image && (
        <img
          src={plotly_image}
          style={{
            width: 'auto',
            height: '300px',
          }}
        />
      ))}
    </MDBox>
  );
};

export default function AssistantMessage() {
    const { current_step, plotly_image, plotly_json_path, final_answer, query_result, error } = useMessage(m => {
      return m.metadata?.custom ?? {};
    });

    // just showing current_step in the steps array
    const steps = [current_step];

    if (error) {
      return (
        <MessagePrimitive.Root className="relative mb-8 flex w-full max-w-3xl gap-4 font-size-12">
          <AssistantAvatar />
          <div className="flex-grow space-y-4">
            <MDTypography variant="button" color="error" fontWeight="regular" sx={{fontSize: "15px !important", lineHeight: "30px !important"}}>
              {error}
            </MDTypography>
          </div>
        </MessagePrimitive.Root>
      );
    }
  
    return (
      <MessagePrimitive.Root className="relative mb-8 flex w-full max-w-3xl gap-4 font-size-12">
        <AssistantAvatar />
        <div className="flex-grow space-y-4">
          <MDBox className="space-y-2">
            {(steps ?? []).length > 0 && <Steps steps={steps} final_answer={final_answer} />}
          </MDBox>

          {query_result && <MDBox mt={2}><PreviewData query_result={query_result} /></MDBox>}
          
          <MDBox mt={2}>
            <MDTypography variant="button" fontWeight="regular" sx={{fontSize: "15px !important", lineHeight: "30px !important"}}>
              {final_answer && <MessagePrimitive.Content components={{Text: FinalAnswerText}}/>}
            </MDTypography>
          </MDBox>

          {(!!plotly_image || !!plotly_json_path) && <Visualization plotly_image={plotly_image} plotly_json_path={plotly_json_path} />}

          <MyAssistantActionBar />
        </div>
      </MessagePrimitive.Root>
    );
  };