import { useRef, useEffect, useState } from "react";
import {
  Thread,
  useThread,
  ThreadWel<PERSON>,
  ThreadList,
  Composer,
  AssistantRuntimeProvider,
  ThreadPrimitive,
  ThreadListPrimitive,
  ThreadListItemPrimitive,
  WebSpeechSynthesisAdapter,
  ThreadListItem,
  useComposerRuntime,
} from "@assistant-ui/react";
import "@assistant-ui/react/styles/index.css";
import MDTypography from "@/components/MDTypography";
import MDBox from "@/components/MDBox";
import MDButton from "@/components/MDButton";
import { useLangGraphRuntime } from "@/layouts/askprashna/langgraph-runtime/index.jsx";
import AssistantMessage from "@/layouts/askprashna/components/AssistantMessage/index.jsx";
import AssistantAvatar from "@/layouts/askprashna/components/AssistantAvatar/index.jsx";
import { createThread, getThreadState, sendMessage, getThreads, cancelRun } from "@/layouts/askprashna/langgraph-api/index.jsx";
import { useTranslation } from "react-i18next";
import Stack from "@mui/material/Stack";
import Skeleton from "@mui/material/Skeleton";
import Menu from "@mui/material/Menu";
import Grid from "@mui/material/Grid";
import AddIcon from "@mui/icons-material/Add";
import { useCancellableAxios } from "@/context/index.jsx";
import { useMaterialUIController } from "@/context/index.jsx";

const MySuggestions = () => {
  const { t } = useTranslation();

  const suggestions = [
    t("prashna.suggestion-1"),
    t("prashna.suggestion-2"),
    t("prashna.suggestion-3")
  ];

  return (
    <Stack
      direction="row"
      className="no-scrollbar"
      spacing={2}
      pb={2}
      sx={{
        overflowX: "auto",
        p: 1,
        justifyContent: "flex-start",
        alignItems: "stretch",
      }}
    >
      {suggestions.map((suggestion, index) => (
        <MDBox key={index}>
          <ThreadPrimitive.Suggestion
            prompt={suggestion}
            method="replace"
            autoSend
            asChild
          >
            <MDButton
              variant="outlined"
              color="secondary"
              sx={{
                width: "25vw",
                minWidth: "150px",
                maxWidth: "300px"
              }}
            >
              <MDTypography variant="button" fontWeight="regular">
                {suggestion}
              </MDTypography>
            </MDButton>
          </ThreadPrimitive.Suggestion>
        </MDBox>
      ))}
    </Stack>
  );
};


const MyThreadWelcome = () => {
  const {t} = useTranslation();
  return (
    <ThreadWelcome.Root>
      <ThreadWelcome.Center>
        <AssistantAvatar />
        <ThreadWelcome.Message message={t("prashna.welcome-message")} />
      </ThreadWelcome.Center>
      <MySuggestions />
    </ThreadWelcome.Root>
  );
};

const MyComposerAction = () => {
  const composerRuntime = useComposerRuntime();

  const handleCancel = () => {
    composerRuntime.cancel(); // Core cancellation method
  };
  return (
    <>
      <ThreadPrimitive.If running={false}>
        <Composer.Send />
      </ThreadPrimitive.If>
      <ThreadPrimitive.If running>
        <Composer.Cancel onClick={handleCancel}/>
      </ThreadPrimitive.If>
    </>
  );
};

const MyComposer = () => {
  const {t} = useTranslation();
  return (
    <Composer.Root>
      <Composer.Input placeholder={t("prashna.composer-placeholder")} autoFocus />
      <MyComposerAction />
    </Composer.Root>
  );
};

const ThreadFollowupSuggestions = () => {
  const suggestions = useThread((t) => t.suggestions);

  return (
    <ThreadPrimitive.If empty={false} running={false}>
      <MDBox width="100%">
        {suggestions?.map((suggestion, idx) => (
          <MDBox key={idx} my={1} width="80%" minWidth="80%" display="flex" justifyContent="center" alignItems="center" mx="auto"> 
          <ThreadPrimitive.Suggestion
            prompt={suggestion.prompt}
            method="replace"
            autoSend
            asChild
          >
              <MDButton
                variant="outlined"
                color="secondary"
                p={1}
                fullWidth
                sx={{
                  width: "100%",
                  height: "100%",
                }}
              >
                <MDTypography variant="button" fontWeight="regular" textAlign="center" width="100%">
                  {suggestion.prompt}
                </MDTypography>
              </MDButton>
          </ThreadPrimitive.Suggestion>
          </MDBox>
        ))}
      </MDBox>
    </ThreadPrimitive.If>
  );
};

const MyThread = (config) => {
  const {currentThreadLoading} = config;

  if (currentThreadLoading) {
    return (
      <Thread.Root config={config}>
        <Thread.Viewport>
          <Grid container spacing={2}>
            <Grid item xs={4}></Grid>
            <Grid item xs={8}>
              <Skeleton variant="text" width={"100%"} height={60} />
            </Grid>
            <Grid item xs={8}>
              <Skeleton variant="text" width={"100%"} height={60} />
            </Grid>
            <Grid item xs={4}></Grid>
          </Grid>
        </Thread.Viewport>
      </Thread.Root>
    );
  }
  return (
    <Thread.Root config={config}>
      <Thread.Viewport>
        <MyThreadWelcome />
        <Thread.Messages
          components={{
            AssistantMessage: AssistantMessage,
          }}
        />
        <ThreadFollowupSuggestions />
        <Thread.ViewportFooter>
          <Thread.ScrollToBottom />
          <MyComposer />
        </Thread.ViewportFooter>
      </Thread.Viewport>
    </Thread.Root>
  );
};

const CustomThreadListItem = () => {
  return (
    <ThreadListItemPrimitive.Root className="max-h-1/2">
      <ThreadListItemPrimitive.Trigger className="w-full h-auto">
        <MDButton
          variant="text"
          color="secondary"
          sx={{
            width: "100%",
            transition: "background-color 0.3s",
            borderRadius: "0",
            "&:hover": {
              backgroundColor: "rgba(0, 0, 0, 0.05)",
            },
          }}
        >
          <MDTypography
            width="100%"
            variant="button"
            color="dark"
            fontWeight="regular"
            textAlign="left"
            sx={{
              display: "-webkit-box",
              WebkitBoxOrient: "vertical",
              WebkitLineClamp: 1,
              overflow: "hidden",
            }}
          >
            <ThreadListItemPrimitive.Title />
          </MDTypography>
        </MDButton>
      </ThreadListItemPrimitive.Trigger>
    </ThreadListItemPrimitive.Root>
  );
};

export function MyAssistant() {
  const {t} = useTranslation();
  const [threads, setThreads] = useState([]);
  const [threadsLoading, setThreadsLoading] = useState(false);
  const [currentThreadLoading, setCurrentThreadLoading] = useState(false);
  const axiosInstance = useCancellableAxios();

  const [controller, dispatch] = useMaterialUIController();
  const {selectedShop, shopConfig, integrations} = controller;

  const uiThreads = threads.filter((thread) => thread.values?.user_query).map((thread) => {
      const title = thread.values?.user_query ?? "New Thread";
      const threadId = thread.thread_id;

      return {
        status: "regular",
        threadId: threadId,
        title: title,
      }
  });

  const threadIdRef = useRef();
  const runtime = useLangGraphRuntime({
    threadId: threadIdRef.current,
    threads: uiThreads,
    adapters: {
      speech: new WebSpeechSynthesisAdapter(),
    },
    unstable_allowCancellation: true, // Enable cancellation support
    stream: async (messages, options) => {
      const { abortSignal, runId } = options || {};

      if (abortSignal) {
        abortSignal.addEventListener('abort', async () => {
          await cancelRun(threadIdRef.current, runId(), axiosInstance);
          // Implement any additional cleanup or state updates here
        });
      }

      if (!threadIdRef.current) {
        const { thread_id } = await createThread(axiosInstance, selectedShop);
        threadIdRef.current = thread_id;
      }
      const threadId = threadIdRef.current;

      // Pass the abortSignal to your sendMessage function or streaming logic
      return sendMessage({
        threadId,
        messages,
        abortSignal, // Ensure your sendMessage function can handle this
      }, axiosInstance, selectedShop);
    },

    onSwitchToNewThread: async () => {
      setCurrentThreadLoading(true);
      const { thread_id } = await createThread(axiosInstance, selectedShop);
      threadIdRef.current = thread_id;
      setCurrentThreadLoading(false);
    },
    onSwitchToThread: async (threadId) => {
      setCurrentThreadLoading(true);
      const state = await getThreadState(threadId, axiosInstance, selectedShop);
      threadIdRef.current = threadId;
      setCurrentThreadLoading(false);
      return { messages: state.values.messages };
    },
  });

  const fetchThreads = async () => {
    setThreadsLoading(true);
    const threads = await getThreads(axiosInstance, selectedShop);
    setThreads(threads);
    setThreadsLoading(false);
  };

  useEffect(() => {
    fetchThreads();
  }, []);
  // Anchor element for the dropdown menu
  const [anchorEl, setAnchorEl] = useState(null);
  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const DesktopThreadList = () => {
    return (
      <div className="hidden md:flex m-2 p-3 w-1/4 bg-white rounded-lg shadow-md flex-col">
        <ThreadList.Root className="flex flex-col h-full">

          <MDBox className="flex flex-col" sx={{ gap: 2 }}>
            <ThreadListPrimitive.New asChild>    
              <MDButton variant="gradient" color="info" fullWidth>
                <AddIcon /> &nbsp;
                {t("prashna.new-question")}
              </MDButton>
            </ThreadListPrimitive.New>
  
            <MDButton variant="outlined" color="secondary">
              {t("prashna.library")}
            </MDButton>
          </MDBox>

          <MDTypography variant="button" color="secondary" fontWeight="medium" p={0} mt={2} pb={0}>
            {t("prashna.history")}
          </MDTypography>

          <MDBox
            sx={{
              maxHeight: "80%",
              borderRadius: "8px",
              overflowY: "auto",
              border: "1px solid #f0f0f0"
            }}
          >
            {threadsLoading &&
              Array.from({ length: 5 }).map((_, index) => (
                <Skeleton
                  key={index}
                  variant="rectangular"
                  height={40}
                  sx={{ mx: 2, my: 2, borderRadius: "8px" }}
                />
              ))}
  
            {!threadsLoading && (
              <ThreadList.Items
                components={{ ThreadListItem: CustomThreadListItem }}
              />
            )}
          </MDBox>
        </ThreadList.Root>
      </div>
    );
  };

  const MobileThreadList = () => {
    return (
      <div className="md:hidden">
        <ThreadList.Root className="mb-4">
          {/* <ThreadListPrimitive.New asChild> */}

          <MDButton variant="outlined" color="secondary" fullWidth>
            <AddIcon /> &nbsp;
            <MDTypography variant="button" fontWeight="regular" color="dark">
              {t("prashna.new-question")}
            </MDTypography>
          </MDButton>

          {/* </ThreadListPrimitive.New> */}
        </ThreadList.Root>
        <MDButton
          variant="outlined"
          color="secondary"
          fullWidth
          onClick={handleMenuOpen}
        >
          {t("prashna.history")}
        </MDButton>
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          PaperProps={{
            style: {
              maxHeight: 300,
              width: "100%",
              marginTop: "8px",
              display: "flex",
              flexDirection: "column",
            },
          }}
        >
          {threadsLoading && (
            <Skeleton variant="text" width="100%" height={60} />
          )}
          {!threadsLoading && (
            <ThreadList.Items
              components={{
                ThreadListItem: CustomThreadListItem,
              }}
            />
          )}
        </Menu>
      </div>
    );
  };

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <article className="h-[83vh] overflow-y-auto text-sm">
        <div className="flex h-full flex-col md:flex-row">
          <DesktopThreadList />
          <MobileThreadList />
          <div className="flex-grow mt-2 p-3 w-full md:w-3/4 bg-white rounded-lg shadow-md m-2">
            <MyThread currentThreadLoading={currentThreadLoading} />
          </div>
        </div>
      </article>
    </AssistantRuntimeProvider>
  );
}

export default MyAssistant;
