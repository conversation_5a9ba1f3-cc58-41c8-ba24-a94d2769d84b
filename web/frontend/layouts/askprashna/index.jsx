import MyAssistant from "@/layouts/askprashna/components/MyAssistant";
import React, { useState } from "react";
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import MDBox from "@/components/MDBox";
import { DashboardLoader } from "@/components/AppLoader";

export default function AskPrashna() {
  const [loading, setLoading] = useState(false);

  return (
    <DashboardLayout>
      <DashboardNavbar sections={[]} />
      <MDBox
        mt={1.5}
        sx={{
          height: "100%",
          width: "100%",
          borderRadius: "16px",
          overflow: "hidden",
          backgroundColor: "white",
          display: "flex",
          flexDirection: "column",
        }}
      >
        {loading && <DashboardLoader />}
        {!loading && <MyAssistant />}
      </MDBox>
      
    </DashboardLayout>
  );
}
