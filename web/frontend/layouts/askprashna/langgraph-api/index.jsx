export const createThread = async (axiosInstance, selectedShop) => {
    try {
        let data = {};
        if (selectedShop) {
            data.selectedShop = selectedShop;
        }

        const response = await axiosInstance.post("/api/agent/thread/create", data);
        return response.data;
    } catch (error) {
        console.error(error);
        return null;
    }
};

export const getThreadState = async (threadId, axiosInstance, selectedShop) => {
  try {
    let data = { threadId };
    if (selectedShop) {
      data.selectedShop = selectedShop;
    }

    const response = await axiosInstance.post(`/api/agent/thread/state`, data);
    return response.data;
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const cancelRun = async (threadId, runId, axiosInstance, selectedShop) => {
  try {
    let data = { threadId, runId };
    if (selectedShop) {
      data.selectedShop = selectedShop;
    }

    const response = await axiosInstance.post(`/api/agent/thread/cancel`, data);
    return response.data;
  } catch (error) {
    console.error(error);
    return null;
  }
};

export const getThreads = async (axiosInstance, selectedShop, limit = 10, status = "idle" ) => {
  try {
    let data = { limit, status };
    if (selectedShop) {
      data.selectedShop = selectedShop;
    }

    const response = await axiosInstance.post(`/api/agent/threads`, data);
    return response.data;
  } catch (error) {
    console.error(error);
    return [];
  }
};
 
export const sendMessage = async (params, axiosInstance, selectedShop) => {
  const { threadId, messages, abortSignal } = params;

  let data = {threadId, messages};
  if (selectedShop) {
    data.selectedShop = selectedShop;
  }

  try {
    const response = await axiosInstance({
      method: 'POST',
      url: `/api/agent/thread/send`,
      data: data,
      responseType: 'stream',
      signal: abortSignal,
      adapter: 'fetch'
    });

    const stream = response.data;
    return stream;
  } catch (error) {
    console.error("Axios request failed:", error);
    throw error;
  }
};