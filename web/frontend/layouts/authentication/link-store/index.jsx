import { useEffect } from "react";
import { fetchLoginConfig, useMaterialUI<PERSON>ontroller, useCancellableAxios } from "@/context";
import { toast } from 'react-toastify';
import { useTranslation } from "react-i18next";
import Card from "@mui/material/Card";
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import CircularProgress from "@mui/material/CircularProgress";
import bgImage from "@/assets/images/bg-sign-in-basic.jpeg";
import BasicLayout from "@/layouts/authentication/components/BasicLayout";

function LinkStore() {
  const [controller, dispatch] = useMaterialUIController();
  const {loginConfig} = controller;
  const {t} = useTranslation();

  const axiosInstance = useCancellableAxios();

  useEffect(() => {
    if (!!loginConfig.linkUserShop) {
        axiosInstance.post(`/api/link-user-shop`, {
          shop_to_link : loginConfig.linkUserShop.myshopify_domain,
        }).then((res) => {
          if (res.data.status) {
            fetchLoginConfig(dispatch, true);
            toast.success(t("store-linking-success"));
          } else {
            toast.error(t("store-linking-failed"));
          }
        }).catch((err) => {
          console.error(err);
          toast.error(t("store-linking-failed"));
        })
    }
  }, [loginConfig.linkUserShop]);

  return (
    <BasicLayout image={bgImage}>
    <MDBox height="100vh" display="flex" flexDirection="column" justifyContent="center" alignItems="center">
    <Card>
      <MDBox display="flex" flexDirection="column" justifyContent="center" alignItems="center" m={4}>
      <MDTypography variant="h6" gutterBottom>
        {t("linking-store")}
      </MDTypography>
      <MDTypography variant="h6" color="info">
        {loginConfig.linkUserShop.domain}
      </MDTypography>
      <MDTypography variant="button" mt={3}>
        <CircularProgress size={25} color="primary" />
      </MDTypography>
      </MDBox>
    </Card>
    </MDBox>
    </BasicLayout>
  );
}

export default LinkStore;