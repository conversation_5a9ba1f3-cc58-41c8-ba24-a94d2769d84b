import React, {useState} from "react";
import {tracker, useMaterialUIController} from "@/context";

// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import DefaultCell from "@/layouts/dashboards/sales/components/DefaultCell";
import ConsolidateViewBlock from "@/components/ConsolidateViewBlock";
import Footer from "@/examples/Footer";
// @mui material components
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import Divider from "@mui/material/Divider";
import DashboardFeatureBlock from "@/examples/DashboardFeatureBlock";
import RadarChart from "@/examples/Charts/RadarChart";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTooltip from "@/components/MDTooltip";
import MDButton from "@/components/MDButton";
import Icon from "@mui/material/Icon";
import MDTypography from "@/components/MDTypography";
import MDBadge from "@/components/MDBadge";
import {shopResults, industryResultsQuery} from './benchmarkQueries';
import GrowthChart from "@/layouts/benchmarks/GrowthChart";
import DataTable from "@/examples/Tables/DataTable";
import { cubejsApi } from "@/context";

import NiceModal, { useModal } from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import premiumTag from "@/assets/images/premium-tag.png";
import { useTranslation } from 'react-i18next';

import GradientLineChart from "@/examples/Charts/LineCharts/GradientLineChart";
import {DashboardLoader} from "@/components/AppLoader";
import Dinero from "dinero.js";
import ReviewBar from "@/examples/ReviewBar";
import { rupeeShort } from "@/util";
import { Element } from 'react-scroll';
import Dialog from '@mui/material/Dialog';
import { InfoOutlinedIcon } from "@/examples/Icons";

const DialogBox = NiceModal.create(({content}) => {
  const modal = useModal();
  const {t} = useTranslation();
  return (
    <Dialog
        open={modal.visible}
        onClose={modal.hide}
        sx={{minWidth:"300px"}}
        TransitionProps={{
            onExited: () => modal.remove(),
        }}
        maxWidth="md"
    >
      {content}
    </Dialog>
  );
});


const ORDERED_SECTIONS = [
    'section-growth-benchmarks',
    'section-ltv-benchmarks',
    'section-repeat-benchmarks',
    'section-frequency-benchmarks',
    'section-rfm-benchmarks',
];

let defaultBenchmarks = {
    "ltv": {
        title: "benchmark-ltv-title",
        section_id : "section-ltv-benchmarks",
        type: "ltv",
        icon: {component : "insights", color : "primary"},
        desc: "benchmark-ltv-desc",
        chart_type : "line",
        metricTip : {
            metricLabel : "1y-ltv",
            shop_val : "ltv1yLocal",
            industry_p25 : "ltv1yP25",
            industry_p75 : "ltv1yP75",
        },
        chart : {
            labels : ["aov", "1m-ltv", "3m-ltv", "6m-ltv", "1y-ltv"],
            customOptions : {
                valSuffix : " "
            },
            datasets: [
                {
                    label: "you",
                    pointStyle: "circle",
                    pointRadius: 3,
                    borderWidth: 4,
                    pointHoverRadius: 5,
                    color: "dark",
                    fill : false,
                    data: [0, 0, 0, 0, 0],
                    accessor : ["shop.aovLocal", "shop.ltv1mLocal", "shop.ltv3mLocal", "shop.ltv6mLocal", "shop.ltv1yLocal"]
                },
                {
                    label: "you",
                    color: "success", // fills 75th - 100th percentile
                    borderWidth: 0,
                    data: [0, 0, 0, 0, 0],
                    fill: false,
                    accessor : ["industry.aovP95", "industry.ltv1mP95", "industry.ltv3mP95", "industry.ltv6mP95", "industry.ltv1yP95"]
                },
                {
                    label: "top-25",
                    color: "success",
                    borderWidth: 0,
                    data: [0, 0, 0, 0, 0],
                    backgroundColor : "rgba(76, 175, 80, 0.3)", // fills 75th - 100th percentile
                    fill: 1,
                    accessor : ["industry.aovP75", "industry.ltv1mP75", "industry.ltv3mP75", "industry.ltv6mP75", "industry.ltv1yP75"]
                },
                {
                    label: "avg",
                    color: "warning",
                    borderWidth: 0,
                    fill: 2,
                    backgroundColor : "rgba(254, 250, 205, 0.8)", // fills 50th - 75th percentile
                    data: [0, 0, 0, 0, 0],
                    accessor : ["industry.aovP50", "industry.ltv1mP50", "industry.ltv3mP50", "industry.ltv6mP50", "industry.ltv1yP50"]
                },
                {
                    label: "bottom-25",
                    color: "warning",
                    borderWidth: 0,
                    backgroundColor: "rgba(254, 250, 205, 0.8)", // fills 25th - 50th percentile
                    fill : 3,
                    data: [0, 0, 0, 0, 0],
                    accessor : ["industry.aovP25", "industry.ltv1mP25", "industry.ltv3mP25", "industry.ltv6mP25", "industry.ltv1yP25"]
                },
                {
                    label: "bottom-25",
                    color: "error",
                    pointHitRadius : 0,
                    borderWidth: 0,
                    hoverBorderWidth: 0,
                    radius : 0,
                    backgroundColor: "rgba(244, 67, 53, 0.3)", // fills 0th - 25th percentile
                    fill : 4,
                    data: [0, 0, 0, 0, 0],
                    accessor : ["industry.aovP5", "industry.ltv1mP5", "industry.ltv3mP5", "industry.ltv6mP5", "industry.ltv1yP5"]
                }
            ],
        },
        table : {
            columns: [
              { HeaderKey: "metric", accessor: "metric" },
              { HeaderKey: "you", accessor: "shop", className: "center-aln" },
              { HeaderKey: "top-25", accessor: "industry_p75", HeaderColor : "success", className: "center-aln" },
              { HeaderKey: "avg", accessor: "industry", HeaderColor : "warning", className: "center-aln"},
              { HeaderKey: "bottom-25", accessor: "industry_p25", HeaderColor : "error", className: "center-aln"},
            ],
            rows: [
              {
                metric : "aov",
                shop: "shop.aovLocal",
                industry: "industry.aovP50",
                industry_p25: "industry.aovP25",
                industry_p75: "industry.aovP75",
              },
              {
                metric : "1m-ltv",
                shop: "shop.ltv1mLocal",
                industry: "industry.ltv1mP50",
                industry_p25: "industry.ltv1mP25",
                industry_p75: "industry.ltv1mP75",
              },
              {
                metric : "3m-ltv",
                shop: "shop.ltv3mLocal",
                industry: "industry.ltv3mP50",
                industry_p25: "industry.ltv3mP25",
                industry_p75: "industry.ltv3mP75",
              },
              {
                metric : "6m-ltv",
                shop: "shop.ltv6mLocal",
                industry: "industry.ltv6mP50",
                industry_p25: "industry.ltv6mP25",
                industry_p75: "industry.ltv6mP75",
              },
              {
                metric : "1y-ltv",
                shop: "shop.ltv1yLocal",
                industry: "industry.ltv1yP50",
                industry_p25: "industry.ltv1yP25",
                industry_p75: "industry.ltv1yP75",
              }
            ],
        }
    },
    "repeat_customers": {
        title: "benchmark-repeat-perc-title",
        section_id : "section-repeat-benchmarks",
        type: "repeat_customers",
        desc: "benchmark-repeat-perc-desc",
        icon: {component : "show_chart", color : "info"},
        chart_type : "line",
        metricTip : {
            metricLabel : "1y-repeat",
            shop_val : "return1y",
            industry_p25 : "return1yP25",
            industry_p75 : "return1yP75",
        },
        chart : {
            labels: ["1m-repeat", "3m-repeat", "6m-repeat", "1y-repeat"],
            customOptions : {
                valSuffix : "%"
            },
            datasets: [
                {
                    label: "you",
                    pointStyle: "circle",
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    color: "dark",
                    fill : false,
                    borderWidth: 4,
                    data: [0, 0, 0, 0],
                    accessor : ["shop.return1m", "shop.return3m", "shop.return6m", "shop.return1y"]
                },
                {
                    label: "you",
                    color: "success", // fills 75th - 100th percentile
                    borderWidth: 0,
                    data: [0, 0, 0, 0, 0],
                    fill: false,
                    accessor : ["industry.return1mP95", "industry.return3mP95", "industry.return6mP95", "industry.return1yP95"]
                },
                {
                    label: "top-25",
                    color: "success",
                    borderWidth: 0,
                    data: [0, 0, 0, 0, 0],
                    backgroundColor : "rgba(76, 175, 80, 0.3)", // fills 75th - 100th percentile
                    fill: 1,
                    accessor : ["industry.return1mP75", "industry.return3mP75", "industry.return6mP75", "industry.return1yP75"]
                },
                {
                    label: "avg",
                    color: "warning",
                    borderWidth: 0,
                    fill: 2,
                    backgroundColor : "rgba(254, 250, 205, 0.8)", // fills 50th - 75th percentile
                    data: [0, 0, 0, 0, 0],
                    accessor : ["industry.return1mP50", "industry.return3mP50", "industry.return6mP50", "industry.return1yP50"]
                },
                {
                    label: "bottom-25",
                    color: "error",
                    borderWidth: 0,
                    backgroundColor: "rgba(254, 250, 205, 0.8)", // fills 25th - 50th percentile
                    fill : 3,
                    data: [0, 0, 0, 0, 0],
                    accessor : ["industry.return1mP25", "industry.return3mP25", "industry.return6mP25", "industry.return1yP25"]
                },
                {
                    label: "bottom-25",
                    color: "error",
                    pointHitRadius : 0,
                    borderWidth: 0,
                    hoverBorderWidth: 0,
                    radius : 0,
                    backgroundColor: "rgba(244, 67, 53, 0.3)", // fills 0th - 25th percentile
                    fill : 4,
                    data: [0, 0, 0, 0, 0],
                    accessor : ["industry.return1mP5", "industry.return3mP5", "industry.return6mP5", "industry.return1yP5"]
                }
            ]
        },
        table : {
            columns: [
              { HeaderKey: "metric", accessor: "metric" },
              { HeaderKey: "you", accessor: "shop", className: "center-aln" },
              { HeaderKey: "top-25", accessor: "industry_p75", HeaderColor : "success", className: "center-aln" },
              { HeaderKey: "avg", accessor: "industry", HeaderColor : "warning", className: "center-aln"},
              { HeaderKey: "bottom-25", accessor: "industry_p25", HeaderColor : "error", className: "center-aln"},
            ],
            rows: [
              {
                metric : "1m-repeat",
                shop: "shop.return1m",
                industry: "industry.return1mP50",
                industry_p25: "industry.return1mP25",
                industry_p75: "industry.return1mP75",
              },
              {
                metric : "3m-repeat",
                shop: "shop.return3m",
                industry: "industry.return3mP50",
                industry_p25: "industry.return3mP25",
                industry_p75: "industry.return3mP75",
              },
              {
                metric : "6m-repeat",
                shop: "shop.return6m",
                industry: "industry.return6mP50",
                industry_p25: "industry.return6mP25",
                industry_p75: "industry.return6mP75",
              },
              {
                metric : "1y-repeat",
                shop: "shop.return1y",
                industry: "industry.return1yP50",
                industry_p25: "industry.return1yP25",
                industry_p75: "industry.return1yP75",
              }
            ],
        }
    },
    "customer_frequency" : {
        title: "benchmark-frequency-title",
        section_id : "section-frequency-benchmarks",
        type: "customer_frequency",
        desc: "benchmark-frequency-desc",
        chart_type : "radar",
        icon : { color: "warning", component: "donut_small" },
        metricTip : {
            metricLabel : "b-2-timers-tip-title",
            shop_val : "twoTimers",
            industry_p25 : "twoTimersP25",
            industry_p75 : "twoTimersP75",
        },
        chart : {
            labels: ["b-2-timers", "b-3-timers", "b-4-5-timers", "b-5plus-timers"],
            customOptions : {
                valSuffix : "%"
            },
            datasets: [
              {
                label: "you",
                color: "dark",
                data: [0, 0, 0, 0 ],
                accessor : ["shop.twoTimers", "shop.threeTimers", "shop.fourToFiveTimers", "shop.fivePlusTimers"]
              },
              {
                label: "avg",
                color: "warning",
                data: [0, 0, 0, 0 ],
                accessor : ["industry.twoTimers", "industry.threeTimers", "industry.fourToFiveTimers", "industry.fivePlusTimers"],
                borderDash: [5, 5]
              }
            ],
        },
        table : {
            columns: [
              { HeaderKey: "metric", accessor: "metric" },
              { HeaderKey: "you", accessor: "shop", className: "center-aln" },
              { HeaderKey: "top-25", accessor: "industry_p75", HeaderColor : "success", className: "center-aln" },
              { HeaderKey: "avg", accessor: "industry", HeaderColor : "warning", className: "center-aln"},
              { HeaderKey: "bottom-25", accessor: "industry_p25", HeaderColor : "error", className: "center-aln"},
            ],
            rows: [
              {
                metric : "b-1-timers",
                shop: "shop.oneTimers",
                industry: "industry.oneTimers",
                industry_p25: "industry.oneTimersP25",
                industry_p75: "industry.oneTimersP75",
              },
              {
                metric : "b-2-timers",
                shop: "shop.twoTimers",
                industry: "industry.twoTimers",
                industry_p25: "industry.twoTimersP25",
                industry_p75: "industry.twoTimersP75",
              },
              {
                metric : "b-3-timers",
                shop: "shop.threeTimers",
                industry: "industry.threeTimers",
                industry_p25: "industry.threeTimersP25",
                industry_p75: "industry.threeTimersP75",
              },
              {
                metric : "b-4-5-timers",
                shop: "shop.fourToFiveTimers",
                industry: "industry.fourToFiveTimers",
                industry_p25: "industry.fourToFiveTimersP25",
                industry_p75: "industry.fourToFiveTimersP75",
              },
              {
                metric : "b-5plus-timers",
                shop: "shop.fivePlusTimers",
                industry: "industry.fivePlusTimers",
                industry_p25: "industry.fivePlusTimersP25",
                industry_p75: "industry.fivePlusTimersP75",
              }
            ],
        }
    },
    "rfm_analysis" : {
        title: "rfm-benchmarks",
        section_id : "section-rfm-benchmarks",
        type: "rfm_analysis",
        desc: "benchmark-rfm-desc",
        chart_type : "radar",
        icon : { color: "warning", component: "donut_small" },
        metricTip : {
            metricLabel : "loyal-tip-title",
            shop_val : "loyalPercentage",
            industry_p25 : "loyalPercentageP25",
            industry_p75 : "loyalPercentageP75",
        },
        chart : {
            labels: ["champion-title", "loyal-title", "promising-title", "new_customers-title", "need_attention-title", "should_not_loose-title", "sleepers-title", "lost-title"],
            customOptions : {
                valSuffix : "%"
            },
            datasets: [
              {
                label: "you",
                color: "dark",
                data: [0, 0, 0, 0, 0, 0, 0, 0],
                accessor : ["shop.championPercentage" , "shop.loyalPercentage", "shop.promisingPercentage", "shop.newCustomersPercentage", "shop.needAttentionPercentage", "shop.shouldNotLoosePercentage", "shop.sleepersPercentage", "shop.lostPercentage"]
              },
              {
                label: "avg",
                color: "warning",
                data: [0, 0, 0, 0, 0, 0, 0, 0],
                accessor : ["industry.championPercentage", "industry.loyalPercentage", "industry.promisingPercentage", "industry.newCustomersPercentage", "industry.needAttentionPercentage", "industry.shouldNotLoosePercentage", "industry.sleepersPercentage", "industry.lostPercentage"],
                borderDash: [5, 5]
              }
            ],
        },
        table : {
            columns: [
              { HeaderKey: "metric", accessor: "metric" },
              { HeaderKey: "you", accessor: "shop", className: "center-aln" },
              { HeaderKey: "top-25", accessor: "industry_p75", HeaderColor : "success", className: "center-aln" },
              { HeaderKey: "avg", accessor: "industry", HeaderColor : "warning", className: "center-aln"},
              { HeaderKey: "bottom-25", accessor: "industry_p25", HeaderColor : "error", className: "center-aln"},
            ],
            rows: [
              {
                metric : "champion-title",
                shop: "shop.championPercentage",
                industry: "industry.championPercentage",
                industry_p25: "industry.championPercentageP25",
                industry_p75: "industry.championPercentageP75",
              },
                {
                metric : "loyal-title",
                shop: "shop.loyalPercentage",
                industry: "industry.loyalPercentage",
                industry_p25: "industry.loyalPercentageP25",
                industry_p75: "industry.loyalPercentageP75",
                },
                {
                metric : "promising-title",
                shop: "shop.promisingPercentage",
                industry: "industry.promisingPercentage",
                industry_p25: "industry.promisingPercentageP25",
                industry_p75: "industry.promisingPercentageP75",
                },
                {
                metric : "new_customers-title",
                shop: "shop.newCustomersPercentage",
                industry: "industry.newCustomersPercentage",
                industry_p25: "industry.newCustomersPercentageP25",
                industry_p75: "industry.newCustomersPercentageP75",
                },
                {
                metric : "need_attention-title",
                shop: "shop.needAttentionPercentage",
                industry: "industry.needAttentionPercentage",
                industry_p25: "industry.needAttentionPercentageP25",
                industry_p75: "industry.needAttentionPercentageP75",
                },
                {
                metric : "should_not_loose-title",
                shop: "shop.shouldNotLoosePercentage",
                industry: "industry.shouldNotLoosePercentage",
                industry_p25: "industry.shouldNotLoosePercentageP25",
                industry_p75: "industry.shouldNotLoosePercentageP75",
                },
                {
                metric : "sleepers-title",
                shop: "shop.sleepersPercentage",
                industry: "industry.sleepersPercentage",
                industry_p25: "industry.sleepersPercentageP25",
                industry_p75: "industry.sleepersPercentageP75",
                },
                {
                metric : "lost-title",
                shop: "shop.lostPercentage",
                industry: "industry.lostPercentage",
                industry_p25: "industry.lostPercentageP25",
                industry_p75: "industry.lostPercentageP75",
                }
            ],
        }
    }
}

var getResults = async function (selectedShop, industry, currency) {
    let result = {
        shop : {},
        industry : {}
    }

    try {
        let cubeApi = cubejsApi(selectedShop, "BenchmarkShops");
        let currShopResults = await cubeApi.load(shopResults)
        let shopResultsData = currShopResults.tablePivot()
        if (shopResultsData.length != 0) {
            for (var k in shopResultsData[0]) {
                result.shop[k.split(".")[1]] = shopResultsData[0][k]
            }
        } else {
            tracker.event("No Data", {report: "benchmarks", missing_value: "shop"})
            return result
        }

        if (result.shop.shopCurrency == "0") {
            tracker.event("No Data", {report: "benchmarks", missing_value: "shop_currency"})
            return result
        }

        
        let cubeApiIndustry = cubejsApi(selectedShop, "BenchmarkIndustry");
        let currIndustryResults = await cubeApiIndustry.load(industryResultsQuery(industry, currency));
        let industryResultsData = currIndustryResults.tablePivot()
        if (industryResultsData.length != 0) {
            for (var k in industryResultsData[0]) {
                result.industry[k.split(".")[1]] = industryResultsData[0][k]
            }
        } else {
            tracker.event("No Data", {report: "benchmarks", missing_value: "industry"})
        }
        
        if (result && result.industry && result.industry.shopCount < 4) {
            tracker.event("No Data", {report: "benchmarks", missing_value: "industry_shops"})
            return result
        }

        return result
    } catch (error) {
        console.error(error)
        tracker.event("Error Occurred", {report: "benchmarks", error})
        return result
    }
}

const RadarBenchmarks = ({data, isSubscriptionInActive}) => {
    const {t} = useTranslation();

    const showTableDialog = () => {
        NiceModal.show(DialogBox, {
            content: (
                <MDBox className="no-scrollbar" sx={{overflow: "scroll", borderRadius:0, pb: 1}} p={1}>
                    <MDTypography variant="h6" color="dark" fontWeight="regular" mb={1} p={1} className="card-title-default">
                        {t(data.title)} 
                    </MDTypography>
                    <DataTable
                            isBasic={true}
                            noEndBorder={true}
                            table={data.table}
                            entriesPerPage={false}
                            showTotalEntries={false}
                            isSorted={false}
                        />
                    <MDBox display="flex" justifyContent="flex-end" alignItems="center" width="100%" mt={1}>
                        <MDButton variant="outlined" color="dark" size="small" my={1} onClick={() => NiceModal.hide(DialogBox)}>
                            {t("close")}
                        </MDButton>
                    </MDBox>
                </MDBox>)
        });
    }

    return (
        <Card>
        <Grid container spacing={3} p={1.6} direction="row" justifyContent="flex-start" alignItems="flex-end">
            <Grid item display="flex" justifyContent="flex-start" alignItems="center">
            <MDTypography variant="h5" color="dark" fontWeight="regular" mr={1} className="card-title-default">
                {t(data.title)}
            </MDTypography>
            <MDTooltip title={t(data.desc)} placement="right">
                <MDBox><InfoOutlinedIcon/></MDBox>
            </MDTooltip>
          </Grid>
        </Grid>
        <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
        <Grid container spacing={1.2} px={1.6} className={isSubscriptionInActive ? "blurBlock" : ""} display="flex" alignItems={"center"} my={1}>
            <Grid item xs={6} md={6} key={data.title}>
                <RadarChart chart={data.chart} />
            </Grid>
            <Grid className="no-scrollbar" item xs={6} md={6} key={data.title + "-table"} sx={{overflow: "scroll", borderRadius:0, pb: 1}}>
                <MDBox bgColor="grey-200" width="100%" lineHeight={1} mb={2} p={1} borderRadius="lg" onClick={showTableDialog} sx={{cursor: "pointer"}}>
                <MDBox display="flex" justifyContent="center" alignItems="center" >
                <InfoOutlinedIcon fontSize="18px" />&nbsp;
                {data.metricTip && <MDTypography variant="button" color="dark" fontWeight="light">
                    <span dangerouslySetInnerHTML={{__html: t(data.metricTip.tipLabel ?? "")}} />
                      : <MDBox component="span" variant="contained" fontWeight="medium" color="text">
                        {t(data.metricTip.metricLabel ?? "")}
                    </MDBox>
                </MDTypography>}
                </MDBox>
                <MDBox display="flex" justifyContent="center" alignItems="center" width="100%" mt={1}>
                    <MDButton variant="outlined" color="dark" size="small" my={1.5} onClick={showTableDialog}>
                        {t("see-table")}
                    </MDButton>
                </MDBox>
                </MDBox>
            </Grid>
        </Grid>
      </Card>
    );
}

const MixedLineChart = ({data, results, isSubscriptionInActive}) => {
    const {t} = useTranslation();

    if (data.type == "ltv") {
        data.chart.customOptions.valSuffix = " " + results.shop.shopCurrency 
    }


    const showTableDialog = () => {
        NiceModal.show(DialogBox, {
            content: (
                <MDBox className="no-scrollbar" sx={{overflow: "scroll", borderRadius:0, pb: 1}} p={1}>
                    <MDTypography variant="h6" color="dark" fontWeight="regular" mb={1} p={1} className="card-title-default">
                        {t(data.title)} 
                    </MDTypography>
                    <DataTable
                            isBasic={true}
                            noEndBorder={true}
                            table={data.table}
                            entriesPerPage={false}
                            showTotalEntries={false}
                            isSorted={false}
                        />
                    <MDBox display="flex" justifyContent="flex-end" alignItems="center" width="100%" mt={1}>
                        <MDButton variant="outlined" color="dark" size="small" my={1} onClick={() => NiceModal.hide(DialogBox)}>
                            {t("close")}
                        </MDButton>
                    </MDBox>
                </MDBox>)
        });
    }

    return (
        <Card>
        <Grid container spacing={3} p={1.6} direction="row" justifyContent="flex-start" alignItems="flex-end">
          <Grid item display="flex" justifyContent="flex-start" alignItems="center">
            <MDTypography variant="h5" color="dark" fontWeight="regular" mr={1} className="card-title-default">
                {t(data.title)}
            </MDTypography>
            <MDTooltip title={t(data.desc)} placement="right">
                <MDBox><InfoOutlinedIcon/></MDBox>
            </MDTooltip>
          </Grid>
        </Grid>
        <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
        <Grid container spacing={1.2} px={1.6} className={isSubscriptionInActive ? "blurBlock" : ""} display="flex" alignItems={"center"} my={1}>
            <Grid item xs={6} md={6} key={data.title}>
                <GradientLineChart chart={data.chart} />
            </Grid>
            <Grid className="no-scrollbar" item xs={6} md={6} key={data.title + "-table"} sx={{overflow: "scroll", borderRadius:0, pb: 1}}>
                <MDBox bgColor="grey-200" width="100%" lineHeight={1} mb={2} p={1} borderRadius="lg" onClick={showTableDialog} sx={{cursor: "pointer"}}>
                <MDBox display="flex" justifyContent="center" alignItems="center" >
                <InfoOutlinedIcon fontSize="18px" />&nbsp;
                {data.metricTip && <MDTypography variant="button" color="dark" fontWeight="light">
                    <span dangerouslySetInnerHTML={{__html: t(data.metricTip.tipLabel ?? "")}} />
                      : <MDBox component="span" variant="contained" fontWeight="medium" color="text">
                        {t(data.metricTip.metricLabel ?? "")}
                    </MDBox>
                </MDTypography>}
                </MDBox>
                <MDBox display="flex" justifyContent="center" alignItems="center" width="100%" mt={1}>
                    <MDButton variant="outlined" color="dark" size="small" my={1.5} onClick={showTableDialog}>
                        {t("see-table")}
                    </MDButton>
                </MDBox>
                </MDBox>
            </Grid>
        </Grid>
      </Card>
    );
}

const GatheringData = () => {
    const {t} = useTranslation();
    return (
        <MDBox 
            display="flex"
            alignItems="center"
            flexDirection="column"
            justifyContent="center"
            >
                <MDBox
                    component="img"
                    sx={{
                        width:"30%",
                    }}
                    src={"https://illustrations.popsy.co/white/meditation-outdoors.svg"}
                    alt={"gethering-data"}
                />
                <MDTypography variant="h6" color="dark" fontWeight="regular">
                    {t("benchmark-nodata")}
                </MDTypography>
                <MDTypography variant="button" color="secondary" fontWeight="regular" mt={2} mx={10}>
                    {t("benchmark-nodata-desc")}
                </MDTypography>
        </MDBox>
    )
}

const BenchmarksGrid = ({benchmarks, results})  => {

    const [controller] = useMaterialUIController();
    const {shopConfig} = controller;

    let isSubscriptionInActive = shopConfig.subscription_enabled && !(shopConfig.planDetails?.features?.industry_benchmarks ?? false);
    const {t} = useTranslation();

    const subscribeToday = !isSubscriptionInActive ? null : (
        <MDBox display="flex" flexDirection="column" alignItems="center" height="40vh" justifyContent="center" width={"100%"} m="auto" my={2}>
            <MDBox
                component="img"
                sx={{
                    width:"25%",
                }}
                src={"https://illustrations.popsy.co/white/business-success-chart.svg"}
                alt={"premium-section"}
            />
            <MDTypography variant="h6" color="dark" fontWeight="regular">
                {t("benchmark-intro")}
            </MDTypography>
            <MDTypography variant="button" color="secondary" fontWeight="regular" mt={2} mx={10}>
                {t("benchmark-intro-2")}
            </MDTypography>
        </MDBox>
    );

    let blurBlockContent = !isSubscriptionInActive ? null : (
        <MDBox
            display="flex"
            flexDirection="column"
            justifyContent="center"
            alignItems="center"
            position="relative"
            width="80%"
            margin="auto"
            top={"-50%"}
            sx={{
                backgroundColor: "transparent",
                zIndex: 2
            }}
        >
            <MDButton variant="contained" color="info" size="small" onClick={() => {
                tracker.event("Paywall", {feature: "industry_benchmarks"});
                NiceModal.show(PaywallDialog, {feature : "industry_benchmarks"})
            }}>
                {t("start-trial-get-access")}
                <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={1} />
            </MDButton>
        </MDBox>
    );

    return (
        <Grid container spacing={3}>

        {/* {subscribeToday} */}

        <Grid item xs={12} md={12}>
            <Element name="section-growth-benchmarks">
            <GrowthChart isEmbedded={false} results={results} isSubscriptionInActive={isSubscriptionInActive} />
            </Element>
            {blurBlockContent}
        </Grid>

        {!!benchmarks && benchmarks.length != 0 && <>
          {benchmarks.map((b) => {
                if (b.chart_type == "line") {
                    return (
                        <Grid item xs={12} md={12} lg={12} key={b.title} >
                            <Element name={b.section_id}>
                            <MixedLineChart data={b} key={b.title} results={results} isSubscriptionInActive={isSubscriptionInActive}/>
                            </Element>
                            {blurBlockContent}
                        </Grid>
                    )
                } else if (b.chart_type == "radar") {
                    return (
                        <Grid item xs={12} md={12} lg={12} key={b.title}>
                            <Element name={b.section_id}>
                            <RadarBenchmarks data={b} key={b.title} isSubscriptionInActive={isSubscriptionInActive} />
                            </Element>
                            {blurBlockContent}
                        </Grid>
                    )
                }
          })}
        </>}

        <MDBox m={2} mb={0}>
            <Divider />
            {/* <MDTypography variant="caption" color="text" fontWeight="regular">
                {t("benchmark-footer")}
            </MDTypography> */}
        </MDBox>
        <ReviewBar />
      </Grid>
    )
}

let formatter = (value, value_type, curr = "USD") => {
    if (value_type == "ltv") {

        if (curr == "INR") {
            return rupeeShort(value)
        }

        return Dinero({amount : Math.round(100*value), currency: curr}).toFormat("$0,0")
    } else if (value_type == "repeat_customers" || value_type == "customer_frequency" || value_type == "rfm_analysis") {
        return `${value}%`
    } else {
        return value
    }
}

let parseResults = (data, t) => {
    let benchmarks = JSON.parse(JSON.stringify(defaultBenchmarks))
    let shopResults = data.shop
    let industryResults = data.industry
    if (Object.keys(industryResults).length == 0) {
        return Object.values(benchmarks)
    }

    for (var k in benchmarks) {
        let shopValues = []
        let industryValues = []

        for (var i in benchmarks[k].chart.labels) {
            benchmarks[k].chart.labels[i] = t(benchmarks[k].chart.labels[i])
        }

        for (var i in benchmarks[k].chart.datasets) {
            let percentiles = []
            for (var j in benchmarks[k].chart.datasets[i].accessor) {
                let accessor = benchmarks[k].chart.datasets[i].accessor[j].split(".")
                let type = accessor[0]
                accessor = accessor[1]
                if (type == "shop" && accessor in shopResults) {
                    percentiles.push(shopResults[accessor])
                    shopValues.push(shopResults[accessor])
                } else if (type == "industry" && accessor in industryResults) {
                    percentiles.push(industryResults[accessor])
                    industryValues.push(industryResults[accessor])
                } else {
                    percentiles.push(0)
                }
            }

            benchmarks[k].chart.datasets[i].data = percentiles
            benchmarks[k].chart.datasets[i].label = t(benchmarks[k].chart.datasets[i].label)
        }

        if (!("table" in benchmarks[k])) {
            continue
        }

        for (var i in benchmarks[k].table.columns) {
            let col = benchmarks[k].table.columns[i]
            if (col.accessor == "industry" || col.accessor == "industry_p25" || col.accessor == "industry_p75") {
                col.Header = (<MDTypography variant="caption" fontWeight="medium" color={col.HeaderColor} textTransform="capitalize">{t(col.HeaderKey)}</MDTypography>)
            } else {
                col.Header = t(col.HeaderKey)
            }
            benchmarks[k].table.columns[i] = col
        }

        for (var i in benchmarks[k].table.rows) {
            let row = benchmarks[k].table.rows[i]
            // evaluate shop color
            let shop_val = 0
            let industry_p25 = 0
            let industry_p75 = 0
            let industry_val = 0
            for (var ri in row) {
                if (ri == "metric") {
                    continue
                }
                let accessor = row[ri].split(".")
                let type = accessor[0]
                accessor = accessor[1]
                if (type == "shop" && accessor in shopResults) {
                    shop_val += shopResults[accessor]
                }

                if (type == "industry" && accessor in industryResults) {
                    if (ri == "industry_p25") {
                        industry_p25 += industryResults[accessor]
                    } else if (ri == "industry_p75") {
                        industry_p75 += industryResults[accessor]
                    } else if (ri == "industry") {
                        industry_val += industryResults[accessor]
                    }
                }
            }

            let shop_color = Math.round(parseFloat(shop_val)) <= Math.round(parseFloat(industry_p25)) ? "error" : (Math.round(parseFloat(shop_val)) >= Math.round(parseFloat(industry_p75)) ? "success" : "warning")
            row.metric = t(row.metric)
            row.shop_color = shop_color
            row.tooltip_text = Math.round(parseFloat(shop_val)) <= Math.round(parseFloat(industry_p25)) ? "b-tooltip-bottom-25-metric" : (Math.round(parseFloat(shop_val)) >= Math.round(parseFloat(industry_p75)) ? "b-tooltip-top-25-metric" : "b-tooltip-avg-metric")
            benchmarks[k].table.rows[i] = row
        }

        if ("metricTip" in benchmarks[k])  {
            let mt = benchmarks[k].metricTip
            let shop_val = shopResults[mt.shop_val] ?? 0;
            let industry_p25 = industryResults[mt.industry_p25] ?? 0;
            let industry_p75 = industryResults[mt.industry_p75] ?? 0;
            benchmarks[k].metricTip.tipLabel = Math.round(parseFloat(shop_val)) <= Math.round(parseFloat(industry_p25)) ? "b-tooltip-bottom-25-metric" : (Math.round(parseFloat(shop_val)) >= Math.round(parseFloat(industry_p75)) ? "b-tooltip-top-25-metric" : "b-tooltip-avg-metric")
            benchmarks[k].metricTip.metricVal = <MDTypography variant="button" color={"text"} fontWeight="medium">{formatter(shop_val, k, shopResults["shopCurrency"])}</MDTypography>
        }

        for (var i in benchmarks[k].table.rows) {
            let row = benchmarks[k].table.rows[i]
            for (var ri in row) {
                if (ri == "metric") {
                    continue
                }
                let accessor = row[ri].split(".")
                let type = accessor[0]
                accessor = accessor[1]
                if (type == "shop" && accessor in shopResults) {
                    row[ri] = (
                        <MDTooltip title={<span dangerouslySetInnerHTML={{__html: t(row.tooltip_text ?? "")}} /> } placement="top">
                            <MDBadge
                            variant="contained"
                            color={row.shop_color}
                            style={{cursor: "default"}}
                            badgeContent={formatter(shopResults[accessor], k, shopResults["shopCurrency"])}
                            container />
                        </MDTooltip>)
                }
                if (type == "industry" && accessor in industryResults) {
                    row[ri] = <DefaultCell>{ri == "industry_p25" ? "< " : (ri == "industry_p75" ? "> " : "")}{formatter(industryResults[accessor], k, shopResults["shopCurrency"])}</DefaultCell>
                }
            }
            benchmarks[k].table.rows[i] = row
        }
    }

    return Object.values(benchmarks)
}

function BenchmarksIndex() {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, shopConfig} = controller;
    const {t} = useTranslation();

    const [benchmarks, setBenchmarks] = React.useState(Object.values(defaultBenchmarks));
    const [results, setResults] = React.useState({});
    const [loading, setLoading] = React.useState(true);

    const industry = shopConfig.shop?.onboard_industry ?? "";
    const currency = shopConfig.shop?.currency ?? "";

    React.useEffect(() => {

        if (selectedShop == "consolidated.myshopify.com") {
            setLoading(false);
            return;
        }

        setLoading(true)
        getResults(selectedShop, industry, currency).then((data) => {
            setResults(data)
            setBenchmarks(parseResults(data, t))
            setLoading(false)
        })
    }, [selectedShop, industry, currency, t]);

    let noData = !results || !results.shop || Object.keys(results.shop).length == 0
    noData = noData || !results || !results.industry || Object.keys(results.industry).length == 0 || results.industry.shopCount < 4

    let isConsolidateViewBlock = selectedShop == "consolidated.myshopify.com";

    let sections = ORDERED_SECTIONS.filter(section => {
        return !isConsolidateViewBlock && !noData;
    });

    return (
        <DashboardLayout>
        <DashboardNavbar sections={sections}/>
        <DashboardFeatureBlock feature={"industry_benchmarks"} />
        <MDBox mt={1.5}>
            {loading && <DashboardLoader />}
            {!loading && isConsolidateViewBlock && <ConsolidateViewBlock />}
            {!loading && !isConsolidateViewBlock && noData && <GatheringData />}
            {React.useMemo(() => {
                if (!loading && !isConsolidateViewBlock && !noData) {
                    return (<BenchmarksGrid benchmarks={benchmarks} results={results} />)
                } else {
                    return null
                }
            }, [benchmarks, results,loading, noData])}
        </MDBox>
        <Footer />
        </DashboardLayout>
    );
}


export default BenchmarksIndex;
