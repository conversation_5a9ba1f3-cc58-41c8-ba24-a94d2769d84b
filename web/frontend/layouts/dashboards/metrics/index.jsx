import React, {useState, useEffect, useMemo} from "react";
// Material Dashboard 2 PRO React examples
import {Link} from "react-router-dom";
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";
import MetricSummary from "@/layouts/dashboards/metrics/MetricSummary";
import {CardLoader} from "@/components/AppLoader";
import ShopMetrics from "@/layouts/dashboards/metrics/ShopMetrics";
import ConnectedAdAccounts from "@/layouts/ads/components/ConnectedAdAccounts";

import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import Divider from "@mui/material/Divider";
import MDTooltip from "@/components/MDTooltip";
import Icon from "@mui/material/Icon";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
// Material Dashboard 2 PRO React examples
import {tracker, useMaterialUIController, useCancellableAxios} from "@/context";
import DatePickerAnt from "@/components/Filters/DatePickerFilter";
import {RelativeGroupingFilter} from "@/components/Filters/TimeFrameFilter";
import { useTranslation } from "react-i18next";
import logoFacebook from "@/assets/images/facebook-logo.png";
import logoGoogleAds from "@/assets/images/google-ads-logo.png";
import RFMTreeMap from "@/layouts/customer/rfm/Treemap";
import {NewVsExistingRoot} from "@/layouts/customer/newvsexisting";
import {CohortAnalysisSummary} from "@/layouts/lifetimevalue/cohortanalysis/Dashboard/CohortAnalysis";
import GrowthChartDashboard from "@/layouts/benchmarks/GrowthChartDashboard";
import {ExploreByUsecase} from "@/layouts/lifetimevalue/cohortanalysis/Dashboard/LTVTrend.jsx";
import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import ReviewBar from "@/examples/ReviewBar";
import { Element } from 'react-scroll';
import {BLENDED, SOURCE_FB, SOURCE_GOOGLE_ADS} from "@/layouts/dashboards/metrics/metadata";

const ORDERED_SECTIONS = [
    'section-shopify-metrics',
    'section-new-vs-existing',
    'section-ltv-growth',
    'section-ltv-cohort-analysis',
    'section-blended-ads',
    'section-rfm-segments',
    'section-growth-benchmarks',
    'section-explore-by-usecase'
];

const BlendedAdsSummary = () => {
    
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, integrations} = controller;
    const {t} = useTranslation();

    const loader = integrations.loading ?? false;
    const [request_ids, footerHelp] = useMemo(() => {
        let fbIntgs = SOURCE_FB in integrations ? integrations[SOURCE_FB] : [];
        let ggIntgs = SOURCE_GOOGLE_ADS in integrations ? integrations[SOURCE_GOOGLE_ADS] : [];
        let request_ids = [];
        for (let intg of fbIntgs) {
            if (intg.synced) {
                request_ids.push(intg.request_id)
            }
        }

        for (let intg of ggIntgs) {
            if (intg.synced) {
                request_ids.push(intg.request_id)
            }
        }

        let footerHelp = (
            <ConnectedAdAccounts preset={BLENDED} />
        );

        return [request_ids, footerHelp];
    }, [integrations]);

    let seeDashboard = request_ids.length > 0;

    if (loader) {
        return (
            <Card>
                <Grid container spacing={3} p={1.6} direction="row" justifyContent="space-between" alignItems="flex-end">
                <Grid item>
                    <MDTypography variant="h5" color="secondary" fontWeight="regular" className="card-title-default">
                        {t("section-blended-ads")}
                    </MDTypography>
                </Grid>
                </Grid>
                <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
                <CardLoader height={300} />
            </Card>
        )
    } else if (seeDashboard) {
        return (
            <MetricSummary
                reportLink={"/ads/blended-summary"}
                footerHelp={footerHelp}
                preset={BLENDED}
                integrationRequestIds={request_ids}
            />
        );
    } else {
        return (
            <Card>
                <Grid container spacing={3} p={1.6} direction="row" justifyContent="space-between" alignItems="flex-end">
                <Grid item>
                    <MDTypography variant="h5" color="secondary" fontWeight="regular" className="card-title-default">
                        {t("section-blended-ads")}
                    </MDTypography>
                </Grid>
                </Grid>
                <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
                <Grid container p={2.5}>
                <MDBox display="flex" justifyContent="center" height={300} alignItems="center" width="100%">
                    <MDBox display="flex" flexDirection="column" alignItems="center">
                        <MDBox>
                            <MDBox component="img" src={logoFacebook} alt="Facebook" width="2rem" mb={1} mr={1} />
                            <MDBox component="img" src={logoGoogleAds} alt="Google Ads" width="2rem" mb={1} />
                        </MDBox>
                        <MDTypography variant="body2" fontWeight="regular" color="text" textAlign="center" mt={1}>
                            {t("section-blended-ads-desc")}
                        </MDTypography>
                        <MDBox mt={2}>
                            <MDBox
                                shadow="md"
                                component={Link} to="/ads/blended-summary"
                                p={1} 
                                sx={{
                                    cursor: "pointer",
                                    borderRadius: ({ borders: { borderRadius } }) => borderRadius.xl,
                                    background : "#49a3f11c",
                                    border: "1px solid #49a3f1"
                                }}
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                                height="3rem"
                                width="8rem"
                            >
                                <MDTypography variant="caption" fontWeight="medium" textTransform="uppercase" sx={{color: "#49a3f1"}}>
                                    {t("connect")}
                                </MDTypography>
                            </MDBox>
                        </MDBox>
                    </MDBox>
                </MDBox>
                </Grid>
            </Card>
        )
    }
}

const NewVsExistingMetrics = () => {
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, selectedFilters, shopConfig} = controller;
    const {t} = useTranslation();
    const {start_date, end_date, time_frame} = selectedFilters;

    const axiosInstance = useCancellableAxios();

    return (
        <NewVsExistingRoot
            selectedShop={selectedShop}
            shopConfig={shopConfig}
            start_date={start_date}
            end_date={end_date}
            time_frame={time_frame}
            hasTable={false}
            hasExport={false}
            axiosInstance={axiosInstance}
            reportLink={"/customer/new-vs-returning-customers"}
            t={t}
        />
    )
}   
    

export default function metrics(props) {
  const [controller, dispatch] = useMaterialUIController();
  const {selectedShop, shopConfig} = controller;
//   const [loader, setLoader] = useState(true);
//   const [integrations, setIntegrations] = useState({});
  
  const { t } = useTranslation();

  let isConsolidateViewBlock = selectedShop == "consolidated.myshopify.com";
  const axiosInstance = useCancellableAxios();

  const subscribeAlertClose = () => {
    let reqData = {type : "subscribe"};
    if (!!selectedShop) {
        reqData.selectedShop = selectedShop;
    }

    tracker.event(`Subscribe Alert Closed`, {
        report: "dashboard"
    });

    //Pass the cancel token to the current request
    axiosInstance
        .post("/api/blacklist", reqData)
        .then(function (response) {
            console.log(response);
        })
        .catch((err) => {
            console.log("error", err);
        });
  }

  const fetchAlerts = () => {

    if (selectedShop == "consolidated.myshopify.com") {
        return;
    }

    let reqData = {};
    if (!!selectedShop) {
        reqData.selectedShop = selectedShop;
    }
    // display subscription alert if not subscribed
    axiosInstance
        .post("/api/alerts", reqData)
        .then(function (response) {
            if (response.data.alerts && response.data.alerts.subscribe) {
                NiceModal.show(PaywallDialog, {feature : "", onExit: subscribeAlertClose});
            }
        })
        .catch((err) => {
            console.log("error", err);
        });
  }

  useEffect(fetchAlerts, [selectedShop]);

  let sections = ORDERED_SECTIONS.filter(section => {
    if (section === 'section-rfm-treemap'
        || section === 'section-blended-ads'
        || section === 'section-growth-benchmarks'
        || section === 'section-explore-by-usecase') {
      return !isConsolidateViewBlock;
    }
    return true;
  });

  const isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;

  const time_limit = shopConfig?.planDetails?.usage_limits?.data_time_limit ?? "unlimited";
  let tooltip_title = t("p-date-block-title-3-m");
  if (time_limit === "1_year") {
      tooltip_title = t("p-date-block-title-1-yr")
  }

  return (
      <DashboardLayout>
          <DashboardNavbar sections={sections} />
            <MDBox mb={3} mt={2}>
                <Card elevation={0} mb={4} my={4}>
                <Grid container spacing={3} className="report-config-panel" direction="row" justifyContent="flex-start" alignItems="center"  px={2} pb={2}>
                    <Grid item>
                        <MDTypography variant="button" sx={{fontSize:"13px"}}>
                            {t("time-period")} &nbsp;{isSubscriptionEnabled && time_limit !== "unlimited" && <MDTooltip title={tooltip_title}>
                                <Icon color="secondary" sx={{fontSize:"14px !important"}}>info_outlined</Icon>
                            </MDTooltip>}
                        </MDTypography>
                        <DatePickerAnt report="dashboard" enableComparison={true} />
                    </Grid>
                    <Grid item>
                        <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("group-by")}</MDTypography>
                        <RelativeGroupingFilter report="dashboard" />
                    </Grid>
                </Grid>
                </Card>
            </MDBox>
          <MDBox mt={1.5}>
            <Grid container spacing={3}>
                <Grid item xs={12} md={12} lg={12}>
                    <Element name="section-shopify-metrics">
                    <ShopMetrics />
                    </Element>
                </Grid>
                <Grid item xs={12} md={12} lg={12}>
                    <Element name="section-new-vs-existing">
                    <NewVsExistingMetrics />
                    </Element>
                </Grid>
                <Grid item xs={12} md={12} lg={12}>
                    <CohortAnalysisSummary />
                </Grid>
                {/* <Grid item xs={12}>
                    <Element name="section-cube-report">
                    <TopCities />
                    </Element>
                </Grid> */}

                {!isConsolidateViewBlock && <Grid item xs={12} md={12} lg={12}>
                    <Element name="section-blended-ads">
                    <BlendedAdsSummary />
                    </Element>
                </Grid>}
                <Grid item xs={12} md={12} lg={12}>
                    <Element name="section-rfm-segments">
                    <RFMTreeMap reportLink={"/customer/rfm-segments"} />
                    </Element>
                </Grid>
                {!isConsolidateViewBlock && <Grid item xs={12} md={12} lg={12}>
                    <Element name="section-growth-benchmarks">
                    <GrowthChartDashboard reportLink={"/benchmarks"} />
                    </Element>
                </Grid>}
                {!isConsolidateViewBlock && <Grid item xs={12} md={12} lg={12}>
                    <Element name="section-explore-by-usecase">
                    <ExploreByUsecase />
                    </Element>
                </Grid>}
            </Grid>
          </MDBox>
          <ReviewBar />
          <Footer />
      </DashboardLayout>
  )
}