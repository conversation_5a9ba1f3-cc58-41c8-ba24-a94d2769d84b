// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import React, {useState, useMemo, useEffect} from "react";
// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";
import {tracker, useMaterialUIController} from "@/context";
import AccountAnalytics from "@/layouts/integrations/components/AccountAnalytics";
import {DashboardLoader} from "@/components/AppLoader";
import ReviewBar from "@/examples/ReviewBar";
import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import ConsolidateViewBlock from "@/components/ConsolidateViewBlock";
import AccountOptionsDropdown from "@/layouts/integrations/components/AccountOptions";
import DashboardFeatureBlock from "@/examples/DashboardFeatureBlock";
import {Integrations} from "@/layouts/integrations";
import { SOURCE_FB } from "@/layouts/dashboards/metrics/metadata";

const ORDERED_SECTIONS = [
  "section-facebook-overview",
  "campaign-performance"
];

export default function facebookOverview() {
  const [controller, dispatch] = useMaterialUIController();
  const {selectedShop, shopConfig, integrations} = controller;

  const loader = integrations.loading ?? false;
  const [selectedAccount, setSelectedAccount] = useState("add-new-account");

  let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
  let blockConnectMultipleAccounts = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.connect_multiple_accounts ?? false);

  let intgs = SOURCE_FB in integrations ? integrations[SOURCE_FB] : [];
  useEffect(() => {
    if ((selectedAccount == "add-new-account" && intgs.length > 0) || intgs.length == 1) {
        setSelectedAccount(intgs[0].request_id);
    }
  }, [intgs]);

  let integration = {}
  for (var k in intgs) {
    if (intgs[k].request_id == selectedAccount) {
      integration = intgs[k]
      break;
    }
  }
  let seeDashboard = integration.synced ?? false;
  let isConsolidateViewBlock = selectedShop == "consolidated.myshopify.com";

  const sections = ORDERED_SECTIONS.filter((section) => {
    return !isConsolidateViewBlock && seeDashboard;
  });

  const handleSetSelectedAccount = (value) => {
    if (value === "add-new-account" && blockConnectMultipleAccounts) {
      tracker.event("Paywall", {feature: "connect_multiple_accounts"});
      NiceModal.show(PaywallDialog, {feature : "connect_multiple_accounts"})
      return false
    }

    setSelectedAccount(value)
  }

  const request_ids = useMemo(() => {
    if (integration.request_id) {
      return [integration.request_id]
    }

    return []
  }, [integration.request_id]);

  return (
      <DashboardLayout>
          <DashboardNavbar sections={sections} />
          <DashboardFeatureBlock feature={"facebook_ads_overview"} />
          <MDBox mt={1.5}>
            {loader && <DashboardLoader />}
            {!loader && isConsolidateViewBlock && <ConsolidateViewBlock />}
            {!loader && !isConsolidateViewBlock && intgs.length > 0 && <MDBox display="flex" direction="row" justifyContent="flex-end"> 
              <AccountOptionsDropdown
                  sourceType={SOURCE_FB}
                  integrations={integrations}
                  selectedAccount={selectedAccount}
                  setSelectedAccount={handleSetSelectedAccount}
              />
              </MDBox>
            }
            {!loader && !isConsolidateViewBlock && !seeDashboard 
              && <Integrations
                  st={SOURCE_FB}
                  request_id={integration.request_id ?? ""}
                  setupMode={selectedAccount == "add-new-account"} 
                />}
            {!loader && !isConsolidateViewBlock && seeDashboard && <AccountAnalytics request_ids={request_ids} preset={SOURCE_FB} />}
          </MDBox>
          {!loader && !isConsolidateViewBlock && seeDashboard && <ReviewBar />}
          <Footer />
      </DashboardLayout>
  )
}