// @mui material components
import {useMemo} from "react";
import {Link, useLocation} from "react-router-dom";
import { toast } from 'react-toastify';
import MDTooltip from "@/components/MDTooltip";
import ConfirmationDialog from '@/components/ConfirmationDialog';
import Card from "@mui/material/Card";
// @mui icons
import MDAvatar from "@/components/MDAvatar";
import MDButton from "@/components/MDButton";
import MDBadge from "@/components/MDBadge";
import DeleteIcon from '@mui/icons-material/Delete';
import DataTable from "@/examples/Tables/DataTable";
import Icon from "@mui/material/Icon";
import {AddStoreDialog} from '@/layouts/authentication/add-store';
import premiumTag from "@/assets/images/premium-tag.png";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import NiceModal from '@ebay/nice-modal-react';


import {useCancellableAxios, tracker, useMaterialUIController, fetchLoginConfig, setSelectedShop} from "@/context";
import {useTranslation} from "react-i18next";
import StorefrontTwoToneIcon from '@mui/icons-material/StorefrontTwoTone';
import StorefrontIcon from '@mui/icons-material/Storefront';


function stringToColor(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = [0, 0, 0];
    for (let i = 0; i < 3; i++) {
        let value = (hash >> (i * 8)) & 0xFF;
        color[i] = (value + 200) / 2;  // Mix with white to create pastel
    }

    return `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
}

function ShopNameCell ({name, myshopify_domain}) {
    return (
        <MDBox display="flex" alignItems="center" pr={2}>
            <MDBox mr={2}>
                <MDAvatar sx={{ bgcolor: stringToColor(name) }} size="md">
                    <StorefrontTwoToneIcon fontSize="small" />
                </MDAvatar>
            </MDBox>
            <MDButton color="secondary" variant="text" component="a" href={`https://${myshopify_domain}`} sx={{fontSize:"13px"}} size="small" target="_blank" rel="noreferrer">
                <MDTypography variant="button" fontWeight="medium">
                    {name}&nbsp;<Icon fontSize="medium">arrow_outward</Icon>&nbsp;
                </MDTypography>
            </MDButton>
        </MDBox>
    )
}

function SubscriptionCell ({subscription, myshopify_domain}) {

    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop} = controller;
    const {t} = useTranslation();
    const { pathname } = useLocation();

    const handleShopChangeForPricing = () => {
        if (selectedShop != myshopify_domain) {
            setSelectedShop(dispatch, myshopify_domain);
        }
        tracker.mixpanel.track('Clicked Pricing Page', {page : pathname, source: "settings_connected_shops"})
    }

    return (
        <MDBox display="flex" alignItems="center" justifyContent="center" pr={2}>
            <MDBox mr={2}>
                {(subscription && subscription.id) ? <MDBadge 
                    badgeContent={
                        <>
                        {subscription.name ?? ""}
                        <img src={premiumTag} alt="premium" style={{width: "15px", height: "15px", marginLeft:"5px"}}/>
                        </>
                    } 
                    size="lg" 
                    p={0}
                    variant="gradient"
                    color="dark"
                    sx={{color:"white", p: 0}}
                /> : 
                    <MDButton color="secondary" variant="text" component={Link} to="/pricing" sx={{fontSize:"13px"}} size="small" onClick={handleShopChangeForPricing}>
                        <MDTooltip title={t("upgrade")} placement="right">
                        <MDTypography variant="button" fontWeight="medium">
                            {t("free")}&nbsp;<Icon fontSize="medium">arrow_outward</Icon>&nbsp;
                        </MDTypography>
                        </MDTooltip>
                    </MDButton>
                }
            </MDBox>
        </MDBox>
    )
}

function ConnectedShopsRoot({settings, fetchSettings}) {

    const [controller, dispatch] = useMaterialUIController();
    const {t} = useTranslation();

    const {connectedShops} = settings;
    const axiosInstance = useCancellableAxios();

    if (!connectedShops || connectedShops.length == 0) {
        return null
    }

    const addNewStore = () => {
        // For now just take the user to sign up after the 
        NiceModal.show(AddStoreDialog, {})
    }

    const unlinkStore = (shop_origin) => {
        NiceModal.show(ConfirmationDialog, {
          title : t("are-you-sure"),
          message: t("store-unlinking-warning"),
          onConfirm : () => {
              axiosInstance.post('/api/unlink-user-shop', {
                shop_to_unlink : shop_origin,
              }).then((res) => {
                if (res.data.status) {
                    fetchSettings();
                  fetchLoginConfig(dispatch, true);
                  toast.success(t("store-unlinking-success"));
                } else {
                  toast.error(t("store-unlinking-failed"));
                }
              }).catch((err) => {
                console.error(err);
                toast.error(t("store-unlinking-failed"));
              })
              
              NiceModal.hide(ConfirmationDialog);
          },
          onCancel : () => {
              NiceModal.hide(ConfirmationDialog);
          }
        })
    }

    let dataTableData = useMemo(() => ({
        columns: [
          { Header: t("store"), accessor: "name", align : "left" },
          { Header: t("plan"), accessor: "subscription", align: "center" },
          { Header: t("unlink"), accessor: "action", align: "center" },
        ],
      
        rows: connectedShops.map(s => (
          {
            name: <ShopNameCell name={s.name} myshopify_domain={s.myshopify_domain} />,
            domain: 
                    <MDButton color="secondary" variant="text" component="a" href={`https://${s.myshopify_domain}`} sx={{fontSize:"13px"}} size="small" target="_blank" rel="noreferrer">{s.domain}</MDButton>
                ,
            subscription: <SubscriptionCell subscription={s.subscription} myshopify_domain={s.myshopify_domain} />,
            action: 
                <MDButton
                variant="outlined"
                color="dark"
                size="small"
                disabled={connectedShops.length == 1}
                sx={{cursor: "pointer"}}
                onClick={() => {unlinkStore(s.myshopify_domain)}} >
                  <DeleteIcon color="dark" fontSize="20px" /> &nbsp;
                  {t("unlink")}
              </MDButton>,
          }
        ))
    }), [connectedShops]);

    return (
        <Card sx={{ boxShadow: "none" }}>
            <MDBox pt={1} pb={2} px={2} lineHeight={1.25}>
            <DataTable
                table={dataTableData}
                entriesPerPage={false}
                showTotalEntries={false}
                isSorted={false}
            />
            <MDBox
                display="flex"
                justifyContent="center"
                alignItems="center"
                width={{ xs: "100%", sm: "auto" }}
                mt={2}
            >
                <MDButton
                    fullWidth
                    variant="outlined"
                    color="dark"
                    size="small"
                    onClick={addNewStore} >
                        <StorefrontIcon color="dark" sx={{fontSize : "20px !important"}}/> &nbsp;
                        {t("add-new-store")}
                </MDButton>
            </MDBox>
            </MDBox>
        </Card>
    );
}

function ConnectedShops(props) {
    const {t} = useTranslation();

  return (
    <Card id="connected-shops">
      <MDBox p={3} lineHeight={1}>
        <MDBox mb={1}>
          <MDTypography variant="h5">
            {t("connected-stores")}
          </MDTypography>
        </MDBox>
        <MDTypography variant="button" color="text">
          {t("manage-connected-stores")}
        </MDTypography>
      </MDBox>
      <ConnectedShopsRoot {...props} />
    </Card>
  );
}

export default ConnectedShops;
