// @mui material components
import Card from "@mui/material/Card";
import Grid from "@mui/material/Grid";
import { useLocation, NavLink, useNavigate } from "react-router-dom";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDAvatar from "@/components/MDAvatar";
import MDButton from "@/components/MDButton";

import { useTranslation } from 'react-i18next';
import { useMaterialUIController } from "@/context";
import { getAuth, signOut } from "firebase/auth";
import {app} from "@/firebase-config"
import Avatar from 'boring-avatars';

function Header() {

    const [controller, dispatch] = useMaterialUIController();
    const {t} = useTranslation();
    const { loginConfig, selectedShop } = controller;
    const navigate  = useNavigate()

    const {userData} = loginConfig;

    if (!userData || !userData.uid) {
        return null
    }

  let handleSignOut = () => {
    const auth = getAuth(app);
    signOut(auth).then(() => {
      navigate('sign-in', {replace:true})
    }).catch((error) => {
      console.error(error)
    });
  }

  return (
    <Card id="user">
      <MDBox p={2}>
        <Grid container spacing={3} alignItems="center">
          <Grid item>
            <MDAvatar alt="profile-image" size="xl" shadow="sm" >
              <Avatar name={userData.onboard_name} variant={"bauhaus"} size={120} colors={['#E91E63','#344767','#F44335','#FB8C00','#f0f2f5']} />
            </MDAvatar>
          </Grid>
          <Grid item>
            <MDBox height="100%" mt={0.5} lineHeight={1}>
              <MDTypography variant="h5" fontWeight="medium">
                {userData.onboard_name && userData.onboard_name.length > 25 ? (userData.onboard_name.substring(0, 25) + "...") : userData.onboard_name}
              </MDTypography>
              <MDTypography variant="button" color="text" fontWeight="medium">
                {userData.email}
              </MDTypography>
            </MDBox>
          </Grid>
          <Grid item xs={12} md={6} lg={3} sx={{ ml: "auto" }}>
            <MDBox
              display="flex"
              justifyContent={{ md: "flex-end" }}
              alignItems="center"
              lineHeight={1}
            >
              <MDButton color="secondary" variant="text" size="small" onClick={handleSignOut}>
                  <Icon sx={{fontSize: "20px !important"}}>logout</Icon>&nbsp;
                  {t("sign-out")}&nbsp;
              </MDButton>
            </MDBox>
          </Grid>
        </Grid>
      </MDBox>
    </Card>
  );
}

export default Header;
