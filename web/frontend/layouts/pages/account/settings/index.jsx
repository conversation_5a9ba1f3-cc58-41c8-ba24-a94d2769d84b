// @mui material components
import React, {useState, useEffect} from "react";
import Grid from "@mui/material/Grid";

// Material Dashboard 2 PRO React components
import MD<PERSON>ox from "@/components/MDBox";
import {DashboardLoader} from "@/components/AppLoader";
import { toast } from "react-toastify";
import { useSearchParams } from 'react-router-dom'
// Settings page components
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";
import Sidenav from "@/layouts/pages/account/settings/components/Sidenav";
import UserHeader from "@/layouts/pages/account/settings/components/UserHeader";
import ConnectedShops from "@/layouts/pages/account/settings/components/ConnectedShops";
import Header from "@/layouts/pages/account/settings/components/Header";
import BasicInfo from "@/layouts/pages/account/settings/components/BasicInfo";
// import Accounts from "@/layouts/pages/account/settings/components/Accounts";
import EmailReports from "@/layouts/pages/account/settings/components/EmailReports";
import Support from "@/layouts/pages/account/settings/components/Support";
import PlansBilling from "@/layouts/pages/account/settings/components/PlansBilling";
import DataSync from "@/layouts/pages/account/settings/components/DataSync";
import GlobalFilters from "@/layouts/pages/account/settings/components/GlobalFilters";
import ChangePassword from "@/layouts/pages/account/settings/components/ChangePassword";
import axios from "axios";
import {useTranslation} from "react-i18next";
import {useCancellableAxios, useMaterialUIController} from "@/context";

import AppBar from "@mui/material/AppBar";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";

// Material Dashboard 2 PRO React base styles
import breakpoints from "@/assets/theme/base/breakpoints";
import ConsolidateViewBlock from "@/components/ConsolidateViewBlock";
import { Integrations } from "@/layouts/integrations";

const settingOptions = {
  "workspace" : [
    { id: "connected-shops" , icon: "add_business", label: "connected-stores" },
    // { id: "team" , icon: "group", label: "team" },
    { id: "support" , icon: "support", label: "support-settings-title" },
  ],
  "user" : [
    { id: "user" , icon: "person", label: "user" },
    // { id: "basic-info" , icon: "receipt_long", label: "basic-info" },
    // { id: "change-password" , icon: "lock", label: "change-password" },
    { id: "support" , icon: "support", label: "support-settings-title" },
  ],
  "store" : [
    { id: "store" , icon: "store_front", label: "store" },
    // { id: "basic-info" , icon: "receipt_long", label: "basic-info" },
    { id: "integrations" , icon: "hub", label: "integrations" },
    { id: "global-filters", icon: "tune", label: "global_filters.title" },
    { id: "scheduled-reports" , icon: "move_to_inbox", label: "scheduled-reports" },
    { id: "subscription" , icon: "stars_rounded", label: "subscription" },
    // { id: "data-sync" , icon: "cloud_sync", label: "data-sync" },
    { id: "support" , icon: "support", label: "support-settings-title" },
  ]
}


function Settings() {

  const {t} = useTranslation();

  const [controller, dispatch] = useMaterialUIController();
  const { selectedShop, loginConfig, shopConfig } = controller;
  const [loader, setLoader] = useState(true);
  const [settings, setSettings] = useState({});
  const [selectedOption, setSelectedOption] = useState("store");

  const [tabsOrientation, setTabsOrientation] = useState("horizontal");
  const [tabValue, setTabValue] = useState("store");
  const [tabDisplay, setTabDisplay] = useState(false);

  const [searchParams, setSearchParams] = useSearchParams();
  let paramErr = searchParams.get('err')
  let selectedOpt = searchParams.get('opt')

  const axiosInstance = useCancellableAxios();

  useEffect(() => {
    if (!!paramErr) {
      toast.error(t("something-went-wrong"));
    }
  }, []);

  useEffect(() => {
    // A function that sets the orientation state of the tabs.
    function handleTabsOrientation() {
      return window.innerWidth < breakpoints.values.sm
        ? setTabsOrientation("vertical")
        : setTabsOrientation("horizontal");
    }

    /** 
     The event listener that's calling the handleTabsOrientation function when resizing the window.
    */
    window.addEventListener("resize", handleTabsOrientation);

    // Call the handleTabsOrientation function to set the state with the initial value.
    handleTabsOrientation();

    // Remove event listener on cleanup
    return () => window.removeEventListener("resize", handleTabsOrientation);
  }, [tabsOrientation]);

  const handleSetTabValue = (event, newValue) => {
    setTabValue(newValue)
    switch (newValue) {
      case "store":
        setSelectedOption("store")
        break;
      case "user":
        setSelectedOption("user")
        break;
      case "workspace":
        setSelectedOption("connected-shops")
        break;
    }
  };

  // on mount check if it's the user flow, if not - remove the appbar
  useEffect(() => {
    if (loginConfig.user && loginConfig.user.email) {
      setTabValue("workspace");
      setSelectedOption("connected-shops");
      setTabDisplay(true);
    } 
  }, []);

  const fetchSettings = () => {
    let reqData = {};
    if (!!selectedShop) {
        reqData.selectedShop = selectedShop;
    }

    setLoader(true);
    axiosInstance.post('/api/settings', reqData)
        .then((response) => {
            setSettings(response.data)
            setLoader(false);
        })
        .catch((error) => {
            console.log(error)
            setLoader(false);
      })
  }

  const updateSettings = (data, finishCallback) => {
    let reqData = {};
    if (!!selectedShop) {
        reqData.selectedShop = selectedShop;
    }

    reqData.settings = data;
    axiosInstance.post('/api/settings/update', reqData)
        .then((res) => {
          if ('error' in res.data) {
            toast.error(res.data.error);
          } else {
            toast.success(t("saved-successfully"));
          }
          finishCallback();
        })
        .catch((error) => {
            console.error(error)
            !axios.isCancel(error) && toast.error(t("something-went-wrong"))
            finishCallback();
        })
  }

  useEffect(fetchSettings, [selectedShop]);

  let availableOptions = settingOptions[tabValue] ?? [];
  let filteredOptions = availableOptions.filter((option) => {
    if (option.id == "subscription") {
      return !!shopConfig.subscription_enabled;
    }
    if (option.id == "global-filters") {
      return !!shopConfig.useMigrationDataset;
    }
    return true;
  });

  useEffect (() => {
    if (selectedOpt && filteredOptions.map((opt) => opt.id).includes(selectedOpt)) {
      setSelectedOption(selectedOpt);
    }
  }, [selectedOpt]);

  let isLoading = loader || shopConfig.loading;
  let isConsolidateViewBlock = tabValue == "store" && selectedShop == "consolidated.myshopify.com";

  return (
    <DashboardLayout>
      <DashboardNavbar absolute={true} />
      <MDBox mt={12}>
        {tabDisplay && <Grid container mb={4}>
          <Grid item xs={12} sm={8} lg={4}>
            <AppBar position="static">
              <Tabs orientation={tabsOrientation} value={tabValue} onChange={handleSetTabValue}>
                <Tab label={t("store")} value="store" />
                <Tab label={t("user")} value="user" />
                <Tab label={t("workspace")} value="workspace" />
              </Tabs>
            </AppBar>
          </Grid>
        </Grid>}
        {isLoading && <DashboardLoader height="85vh" />}
        {!isLoading && isConsolidateViewBlock && <ConsolidateViewBlock />}
        {!isLoading && !isConsolidateViewBlock && <Grid container spacing={3}>
          <Grid item xs={12} lg={3}>
            <Sidenav options={filteredOptions} setSelectedOption={setSelectedOption} selectedOption={selectedOption} />
          </Grid>
          <Grid item xs={12} lg={9}>
            <MDBox mb={3}>
              <Grid container spacing={3}>
                {'store' == selectedOption && <Grid item xs={12}>
                  <Header />
                </Grid>}
                {'store' == selectedOption && <Grid item xs={12}>
                  <BasicInfo updateSettings={updateSettings} />
                </Grid>}
                {'store' == selectedOption && <Grid item xs={12}>
                  <DataSync />
                </Grid>}
                {'user' == selectedOption && <Grid item xs={12}>
                  <UserHeader />
                </Grid>}
                {'user' == selectedOption && <Grid item xs={12}>
                  <ChangePassword />
                </Grid>}
                {'connected-shops' == selectedOption && <Grid item xs={12}>
                  <ConnectedShops settings={settings} fetchSettings={fetchSettings} />
                </Grid>}
                {'integrations' == selectedOption && <Grid item xs={12}>
                  <Integrations />
                </Grid>}
                {'scheduled-reports' == selectedOption && <Grid item xs={12}>
                  <EmailReports settings={settings} updateSettings={updateSettings} fetchSettings={fetchSettings}/>
                </Grid>}
                {'subscription' == selectedOption && <Grid item xs={12}>
                  <PlansBilling />
                </Grid>}
                {'global-filters' == selectedOption && <Grid item xs={12}>
                  <GlobalFilters />
                </Grid>}
                {'support' == selectedOption && <Grid item xs={12}>
                  <Support />
                </Grid>}
              </Grid>
            </MDBox>
          </Grid>
        </Grid>}
      </MDBox>
      <Footer />
    </DashboardLayout>
  );
}

export default Settings;
