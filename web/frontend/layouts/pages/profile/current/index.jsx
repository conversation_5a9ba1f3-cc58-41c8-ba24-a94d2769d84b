import React, {useState} from "react";


import { useLocation } from "react-router-dom";
import MDTypography from "@/components/MDTypography";
import Icon from "@mui/material/Icon";

// Material Dashboard 2 PRO React examples
import SidenavCollapse from "@/examples/Sidenav/SidenavCollapse";
import MDBadge from "@/components/MDBadge";
import {Avatar} from "antd";
import Tooltip from "@mui/material/Tooltip";
import MDButton from "@/components/MDButton";
import MultiStoreDialog from '@/layouts/authentication/add-store/multi-store-account';
import {AddStoreDialog} from '@/layouts/authentication/add-store';
import ConfirmationDialog from '@/components/ConfirmationDialog';
import NiceModal from '@ebay/nice-modal-react';
import { useTranslation } from 'react-i18next';
import AddBusinessIcon from '@mui/icons-material/AddBusiness';

import TextField from '@mui/material/TextField';
import MDBox from "@/components/MDBox";
import Autocomplete from '@mui/material/Autocomplete';
import Chip from "@mui/material/Chip";
import IconButton from "@mui/material/IconButton";
import Checkbox from '@mui/material/Checkbox';
import Menu from "@mui/material/Menu";
import {navbarIconButton} from "@/examples/Navbars/DashboardNavbar/styles";

// Material Dashboard 2 PRO React context
import {
  useMaterialUIController,
  setSelectedShop,
  tracker,
} from "@/context";

const handleSelectedShopChange = (dispatch, selectedShop, t) => {

    if (!selectedShop) {
      return
    }

    tracker.event("Switch Shop", {shop : selectedShop})
    if (selectedShop == 'add-new-store') {
        // show a dialog to add a new store 
        NiceModal.show(AddStoreDialog, {})
        return
    }
  
    if (selectedShop == 'multi-store-sign-up') {
        // show a dialog to multi store login
        NiceModal.show(MultiStoreDialog, {})
        return
    }

    if (selectedShop == 'multi-store-sign-in') {
        // show a dialog to multi store login
        NiceModal.show(MultiStoreDialog, {
            preferSignIn : true  
        })
        return
    }

    if (selectedShop == 'consolidated.myshopify.com') {
        NiceModal.show(ConfirmationDialog, {
            title : t("consolidated-beta-title"),
            message: t("consolidated-beta-message"),
            confirmColor : "info",
            onConfirm : () => {
                setSelectedShop(dispatch, selectedShop)
                NiceModal.hide(ConfirmationDialog);
            },
            onCancel : () => {
                NiceModal.hide(ConfirmationDialog);
            }
        })
        return
    }

    setSelectedShop(dispatch, selectedShop)
}


export function ConsolidatedViewDropdown({light}) {
    const [controller, dispatch] = useMaterialUIController();
    const {loginConfig, shopConfig} = controller;
    const {t} = useTranslation();
    const [anchorEl, setAnchorEl] = useState(null);

    const handleOpen = (event) => {
        setAnchorEl(event.currentTarget);
    };
      
    const handleClose = () => {
        setAnchorEl(null);
    };

    if (!loginConfig.user || !loginConfig.user.email) {
        return null
    }

    if (!loginConfig.shopOptions || loginConfig.shopOptions.length == 0) {
        return null
    }

    if (!shopConfig.shop || shopConfig.shop.myshopify_domain != "consolidated.myshopify.com") {
        return null
    }

    if (!shopConfig.shop.shop_id || !Array.isArray(shopConfig.shop.shop_id)) {
        return null
    }

    let optionList = loginConfig.shopOptions.filter(s => s.is_store).map((s) => {
        return {
            label : s.name,
            value : s.shop_id,
            checked : shopConfig.shop.shop_id.indexOf(s.shop_id) != -1
        }
    })

    return (
        <MDBox onMouseEnter={handleOpen} onMouseLeave={handleClose} sx={{display:"flex", flexDirection:"column", alignItems:"center"}}>
            <Chip 
                aria-label="more"
                aria-controls="long-menu"
                aria-haspopup="true"
                sx={{
                  ".MuiChip-label": {
                    paddingLeft: 0.5,
                    paddingRight: 1.5
                  }
                }}
                color="info"
                icon={<IconButton
                  size="small"
                  disableRipple
                  color="inherit"
                  sx={navbarIconButton}
                  aria-controls="lang-menu"
                  aria-haspopup="true"
                  variant="contained"
                >
                  <Icon>join_full</Icon>
                </IconButton>} label={t("consolidated")} variant="outlined" />
            <Menu
                id="long-menu"
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleClose}
                anchorReference={null}
                anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "left",
                }}
                sx={{ mt: 1, ".MuiMenu-paper": { maxWidth: "300px" }}}
            >
                <MDTypography
                    variant="button"
                    color={"dark"}
                    sx={{"cursor" : "default"}}
                    p={1}
                    mb={1}
                    display="block"
                    textAlign="center"
                >
                    {t("consolidated-shops-message")}
                </MDTypography>
              {optionList.map((option) => (
                <MDBox key={option.value}>
                  <Checkbox checked={option.checked} sx={{"cursor" : "default"}} disableRipple size="small" />
                  <MDTypography variant="button" color={"dark"} sx={{"cursor" : "default"}}>
                    {option.label}
                  </MDTypography>
                </MDBox>
              ))}
                <MDBox sx={{display:"flex", justifyContent:"center"}} mt={1}>
                    <MDButton
                        variant="outlined"
                        size="small"
                        color={"info"}
                        onClick={handleClose}
                        m={2}
                        p={0}
                    >
                        {t("ok")} 
                    </MDButton>
                </MDBox>
            </Menu>
        </MDBox>
    );
}


export function ShopOptionsDropdown() {
    const [controller, dispatch] = useMaterialUIController();
    const {loginConfig, selectedShop} = controller;
    const {t} = useTranslation();

    if (!loginConfig.user || !loginConfig.user.email) {
        return null
    }

    if (!loginConfig.shopOptions || loginConfig.shopOptions.length == 0) {
        return null
    }

    let activeShopObj = {}

    let optionList = loginConfig.shopOptions.filter(s => s.myshopify_domain != "consolidated.myshopify.com").map((s) => {
        let renderName = !!s.nameKey ? t(s.nameKey) : s.name;
        let option = {label : renderName, value : s.myshopify_domain, is_store : (s.is_store ?? false)}
        if (s.myshopify_domain == selectedShop) {
            activeShopObj = option
        }
        return option
    })

    return (
        <Autocomplete
            onChange={(event, newValue) => {
                if (newValue && newValue.value) {
                    handleSelectedShopChange(dispatch, newValue.value, t)
                }
            }}
            getOptionLabel={(option) => {
                return option.label
            }}
            id="shop-selection"
            blurOnSelect
            isOptionEqualToValue={(option, value) => {
                return option.value === value.value;
            }}
            disableClearable={true}
            options={optionList}
            value={activeShopObj}
            fullWidth
            sx={{ width: 300 }}
            renderInput={(params) => <TextField {...params} />}
            renderOption={(props, option) => {
                return (
                    <li {...props} key={option.value}>
                        <MDBox display="flex" flexDirection="column">
                            <MDTypography variant="button" color={"dark"}>{option.label}</MDTypography>
                            <MDTypography variant="caption" fontWeight="regular" color={"dark"}>
                                {option.is_store ? option.value : "" }
                            </MDTypography>
                        </MDBox>                            
                    </li>
                );
            }}
        />
    );
}

export default function ProfileCollapse({keyName, collapseOnClick, openCollapse, color, setOpenCollapse}) {

    let key = keyName;

    const [controller, dispatch] = useMaterialUIController();
    const { loginConfig, selectedShop, shopConfig } = controller;
    const location = useLocation();
    const { pathname } = location;
    const collapseName = pathname.split("/").slice(1)[0];
    const {t} = useTranslation();

    let displayName = "";
    let activeShop = "";

    if (!selectedShop && !!shopConfig.shop) {
        displayName = shopConfig.shop.name
        activeShop = shopConfig.shop.myshopify_domain
    } else if (!!selectedShop && !!shopConfig.shop && shopConfig.shop.myshopify_domain == selectedShop) {
        displayName = shopConfig.shop.name
        activeShop = shopConfig.shop.myshopify_domain
    } else if (!!selectedShop) {
        displayName = selectedShop.replace(".myshopify.com", "");
        activeShop = selectedShop;
    }

    loginConfig.shopOptions.map((s) => {
        if (s.myshopify_domain == activeShop && s.domain != "") {
            displayName = !!s.nameKey ? t(s.nameKey) : s.name
        }
    })

    if (activeShop == "consolidated.myshopify.com") {
        displayName = t("consolidated")
    }

    if (displayName.length > 16) {
        displayName = `${displayName.substring(0, 16)}...`
    }

    return (
        <SidenavCollapse
            key={key}
            name={<div>
                    {displayName}
                </div>}
            icon={
                <Avatar style={{
                        backgroundColor:"#e91e63",
                        verticalAlign: 'middle',
                        justifyContent:"middle",
                        textTransform:"uppercase"
                    }} size="small" >
                    {displayName.length > 0 ? displayName[0] : "x"}
                </Avatar>
            }
            active={collapseName == key}
            open={openCollapse === key}
            onClick={collapseOnClick}
        >
            {loginConfig.shopOptions.slice(0,50).map((s) => {
                let renderName = !!s.nameKey ? t(s.nameKey) : s.name;
                let renderIcon = <Avatar style={{
                                    verticalAlign: 'middle',
                                    justifyContent:"middle",
                                    textTransform:"uppercase"
                                }} size="small" >
                                {renderName[0]}
                            </Avatar>;
                let badge = null
                if (s.myshopify_domain == "consolidated.myshopify.com") {
                    badge = <MDBadge badgeContent={t("beta")} color="secondary" container size="xs" ml={0.5}/>
                }
                if (s.myshopify_domain == "multi-store-sign-in" || s.myshopify_domain == "multi-store-sign-up" || s.myshopify_domain == "add-new-store") {
                    renderIcon = <AddBusinessIcon color="primary" sx={{fontSize : "20px !important"}} />
                }

                return (
                    <SidenavCollapse
                        key={s.myshopify_domain}
                        name={<Tooltip title={s.domain} placement="right">
                                <div>
                                    {renderName} &nbsp; {badge}
                                </div>
                                </Tooltip>}
                        badge={null}
                        icon={renderIcon}
                        noCollapse={true}
                        active={s.myshopify_domain == activeShop}
                        sx={{pointer:"default !important"}}
                        onClick={() => {
                            if (s.myshopify_domain != activeShop) {
                                handleSelectedShopChange(dispatch, s.myshopify_domain, t)
                            }
                            setOpenCollapse(false)
                        }}
                    />
                )
            })}
        </SidenavCollapse>
    )
}