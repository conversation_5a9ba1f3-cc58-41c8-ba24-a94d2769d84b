import React, {useState, useEffect, useMemo} from "react";
import axios from "axios";
import dayjs from 'dayjs';
import { CSVLink } from "react-csv";
// Material Dashboard 2 PRO React examples
import DashboardLayout from "@/examples/LayoutContainers/DashboardLayout";
import Alert from "@mui/material/Alert";
import DashboardNavbar from "@/examples/Navbars/DashboardNavbar";
import Footer from "@/examples/Footer";
import {DashboardLoader} from "@/components/AppLoader";
import FilterDrawer from "@/components/Filters/FilterDrawer";

import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import Divider from "@mui/material/Divider";
import MDTooltip from "@/components/MDTooltip";
import Icon from "@mui/material/Icon";
// Material Dashboard 2 PRO React components
import MDBox from "@/components/MDBox";
import MDTypography from "@/components/MDTypography";
import MDButton from "@/components/MDButton";
// Material Dashboard 2 PRO React examples
import { useCancellableAxios, tracker, useMaterialUIController} from "@/context";
import DashboardFeatureBlock from "@/examples/DashboardFeatureBlock";
import DatePickerAnt from "@/components/Filters/DatePickerFilter";
import { useTranslation } from "react-i18next";
import NiceModal from '@ebay/nice-modal-react';
import PaywallDialog from '@/components/Paywall';
import ReviewBar from "@/examples/ReviewBar";
import { Element } from 'react-scroll';
import PillBar from '@/components/PillBar';
import premiumTag from "@/assets/images/premium-tag.png";
import ProductCell from "@/layouts/product/cartanalysis/components/ProductCell";
import FeatureBlurBox from "@/components/FeatureBlurBox";

// Material Dashboard 2 PRO React examples
import DataTable from "@/examples/Tables/DataTable";

const ORDERED_SECTIONS = [
    'section-basket-analysis'
];

import ConsolidateBlock from "@/components/ConsolidateViewBlock";


const cartAnalysisBreakdowns = [
    "product_title",
    "product_type",
    "product_vendor",
    "sku"
];

let cancelToken;

function DefaultCell({ children }) {
  return (
    <MDTypography variant="button" color="dark" fontWeight="regular">
      {children}
    </MDTypography>
  );
}

const CartAnalysis = ({carts, breakdown}) => {
    const {t} = useTranslation();
    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, shopConfig} = controller;
  
    let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
    let isExportInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.basket_analysis_export ?? false);


    let columns = [
        { Header: t("product-comb"), accessor: "product", width: "10%" },
        { Header: t("product_names"), accessor: "product_names", show : false },
        { Header: t("orders"), accessor: "orders" },
        { Header: t("total_sales") , accessor: "total_sales" },
        { Header: t("order_percentage"), accessor: "order_percentage" },
        { Header: t("aov"), accessor: "aov" },
        { Header: t("new_orders"), accessor: "new_orders" },
        { Header: t("new_total_sales"), accessor: "new_total_sales" },
        { Header: t("repeat_orders"), accessor: "repeat_orders" },
        { Header: t("repeat_total_sales"), accessor: "repeat_total_sales" },
    ];


    const handleExport = () => {
        if (isExportInActive) {
            NiceModal.show(PaywallDialog, {feature : "basket_analysis_export"})
            tracker.event("Paywall", {feature: "basket_analysis_export"})
            return false
        }
        tracker.event("Data Exported", {report: "basket-analysis"})
    }

    const dataTableData = useMemo(() => {
        return {
            columns,
            rows: carts.map(c => {

                let names = c.products.map(p => (p.breakdown ?? ""));

                return {
                    product: <ProductCell products={c.products} breakdown={breakdown} />,
                    product_names : names.join(", "),
                    orders: <DefaultCell>{c.orders ?? '0'}</DefaultCell>,
                    total_sales: <DefaultCell>{c.total_sales_display ?? '0'}</DefaultCell>,
                    order_percentage: <DefaultCell>{c.order_percentage ?? '0'}</DefaultCell>,
                    aov: <DefaultCell>{c.aov_display ?? '0'}</DefaultCell>,
                    new_orders: <DefaultCell>{c.new_orders ?? '0'}</DefaultCell>,
                    new_total_sales: <DefaultCell>{c.new_total_sales_display ?? '0'}</DefaultCell>,
                    repeat_orders: <DefaultCell>{c.repeat_orders ?? '0'}</DefaultCell>,
                    repeat_total_sales: <DefaultCell>{c.repeat_total_sales_display ?? '0'}</DefaultCell>
                }
            })
        }
    }, [carts]);

    let exportBtn = useMemo(() => {

        let headers = columns.map(c => {
            if (c.show === false) {
                return null;
            }
            return {
                label: c.Header,
                key: c.accessor
            }
        }).filter(h => h !== null);

        let data = [];
        try {
            data = carts.map(c => {
                let names = c.products.map(p => (p.breakdown ?? ""));
                return {
                    product: names.join(", "),
                    orders: c.orders ?? '0',
                    total_sales: c.total_sales ?? '0',
                    order_percentage: c.order_percentage ?? '0',
                    aov: c.aov ?? '0',
                    new_orders: c.new_orders ?? '0',
                    new_total_sales: c.new_total_sales ?? '0',
                    repeat_orders: c.repeat_orders ?? '0',
                    repeat_total_sales: c.repeat_total_sales ?? '0'
                }
            })

        } catch(err) {
            console.log(err)
        }

        return (
            <CSVLink
                data={data}
                headers={headers}
                filename={"Cart Analysis.csv"}
                onClick={handleExport}
            >
                <MDBox>
                    <MDButton variant="outlined" color="dark" size="small">
                        <Icon>description</Icon>
                        &nbsp;{t("export-csv")}
                        {isExportInActive && <MDBox component="img" src={premiumTag} alt="Brand" width="1rem" ml={0.8} />}
                    </MDButton>
                </MDBox>
            </CSVLink>
        )
    }, [carts, isExportInActive]);

    return (
        <Card sx={{ overflow: "visible" }}>
            <Grid container spacing={3} p={1.6} direction="row" justifyContent="space-between" alignItems="flex-end">
            <Grid item>
                <MDTypography variant="h5" color="secondary" fontWeight="regular" className="card-title-default">
                {t("basket-analysis")}
                </MDTypography>
            </Grid>
            <Grid item>
                <MDBox display="flex" direction="row" alignItems="center">
                    <MDBox ml={1}>
                        {exportBtn}
                    </MDBox>
                </MDBox>
            </Grid>
            </Grid>
            <Divider flexItem variant="fullWidth" sx={{margin:"0 !important", width: "100%"}} />
            <MDBox px={1.6} pt={1} pb={3}>
                <FeatureBlurBox feature="basket_analysis">
              <DataTable
                canSearch={true}
                table={dataTableData}
                entriesPerPage={true}
                showTotalEntries={true}
                isSorted={true}
                initialState={{
                    hiddenColumns:["product_names"]
                }}
              />
                </FeatureBlurBox>
          </MDBox>
        </Card>
    )
}

function ProductCartAnalysis() {

    const [controller, dispatch] = useMaterialUIController();
    const {selectedShop, selectedFilters, loginConfig, shopConfig} = controller;
    const [loader, setLoader] = useState(true);
    const [cartBundles, setCartBundles] = useState([]);
    const [breakdown, setBreakdown] = useState(cartAnalysisBreakdowns[0]);
    const [minProductCount, setMinProductCount] = useState("2");
    const [basketSize, setBasketSize] = useState("any");
    const {start_date, end_date, applied_filters, filter_version} = selectedFilters;
    const { t } = useTranslation();
    const axiosInstance = useCancellableAxios();
  
    let isSubscriptionEnabled = shopConfig.subscription_enabled ?? false;
    let isSubscriptionInActive = isSubscriptionEnabled && !(shopConfig.planDetails?.features?.basket_analysis ?? false);

    let shouldFetch = !shopConfig.loading && !isSubscriptionInActive && !(!!selectedShop && shopConfig.shop?.myshopify_domain !== selectedShop)

    const fetchCartBundles = (should_track = false) => {
        if (selectedShop == "consolidated.myshopify.com") {
            setLoader(false);
            return;
        }

        if (!shouldFetch) {
            setLoader(false);
            return;
        }

        if (typeof cancelToken != typeof undefined) {
            cancelToken.cancel("Operation canceled due to new request.");
        }
    
        //Save the cancel token for the current request
        cancelToken = axios.CancelToken.source();
    
        let reqData = {
            start_date: dayjs(start_date).format("YYYY-MM-DD"),
            end_date: dayjs(end_date).format("YYYY-MM-DD"),
            breakdown,
            combination_size: minProductCount,
            basket_size: basketSize,
            applied_filters: applied_filters
        };

        if (!!selectedShop) {
            reqData.selectedShop = selectedShop;
        }

        if (should_track) {
            tracker.mixpanel.track("Data Requested", {...reqData, report: "basket-analysis"});
        }
    
        setLoader(true);
        axiosInstance.post('/api/basket-analysis', reqData, { cancelToken: cancelToken.token })
            .then((response) => {
                console.log(response)
                if (response.data && response.data.carts) {
                    setCartBundles(response.data.carts);
                }
                setLoader(false);
            })
            .catch((error) => {
                console.log(error)
                if (!axios.isCancel(error)) {
                    setLoader(false);
                }
            })
    };

    useEffect(fetchCartBundles, [selectedShop, shouldFetch, breakdown, start_date, end_date, minProductCount, basketSize, filter_version]);
    

    let breakdownOptions = cartAnalysisBreakdowns.map((b) => {
        if (isSubscriptionInActive) {
            return {
                label : t(b),
                isPremiumOption : true,
                value : b
            }
        }
        return {
            label : t(b),
            value : b
        }
    });

    const handleBreakdownChange = (value) => {

        tracker.mixpanel.track(`Switch Breakdown`, {
            report: "basket-analysis",
            old_value: breakdown,
            new_value: value
        });

        if (isSubscriptionInActive) {
            NiceModal.show(PaywallDialog, {feature : "basket_analysis"})
            tracker.event("Paywall", {feature: "basket_analysis"})
            return false;
        }

        setBreakdown(value);
    };

    let isConsolidateViewBlock = selectedShop == "consolidated.myshopify.com";

    const time_limit = shopConfig?.planDetails?.usage_limits?.data_time_limit ?? "unlimited";
    let tooltip_title = t("p-date-block-title-3-m");
    if (time_limit === "1_year") {
        tooltip_title = t("p-date-block-title-1-yr")
    }

    return (
        <DashboardLayout>
        <DashboardNavbar sections={ORDERED_SECTIONS} />
        <DashboardFeatureBlock feature={"basket_analysis"} />
            <MDBox mb={3} mt={2}>
                <Card elevation={0} mb={4} my={4}>
                <Grid container spacing={3} className="report-config-panel" direction="row" justifyContent="flex-start" alignItems="center"  px={2} pb={2}>
                    <Grid item>
                        <MDTypography variant="button" sx={{fontSize:"13px"}}>
                            {t("time-period")} &nbsp;{isSubscriptionEnabled && time_limit !== "unlimited" && <MDTooltip title={tooltip_title}>
                                <Icon color="secondary" sx={{fontSize:"14px !important"}}>info_outlined</Icon>
                            </MDTooltip>}
                        </MDTypography>
                        <DatePickerAnt report="basket-analysis"/>
                    </Grid>
                    {breakdownOptions.length > 1 && <Grid item>
                        <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("breakdown-by")}</MDTypography>
                        <PillBar
                            isDropdown={true}
                            options={breakdownOptions}
                            value={breakdown}
                            onChange={handleBreakdownChange}
                        />
                    </Grid>}
                    <Grid item>
                        <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("gen-combinations")}</MDTypography>
                        <PillBar
                            name="type"
                            options={[
                                {label: "2", value: "2"},
                                {label: "3", value: "3"},
                                {label: "4", value: "4"},
                                {label: "5", value: "5"}
                            ]}
                            value={minProductCount}
                            onChange={setMinProductCount}
                        />
                    </Grid>
                    <Grid item>
                        <MDTypography variant="button" sx={{fontSize:"13px"}}>{t("include-basket-size")}</MDTypography>
                        <PillBar
                            name="type"
                            isDropdown={true}
                            options={[
                                {label: t("x-items-or-more", {x: minProductCount}), value: "any"},
                                {label: t("exactly-x-items", {x: minProductCount}), value: "exact"},
                            ]}
                            value={basketSize}
                            onChange={setBasketSize}
                        />
                    </Grid>
                    <Grid item>
                        <MDTypography variant="button" sx={{fontSize:"13px"}}>
                            {t("filters")} &nbsp;{<MDTooltip title={t("filters-tooltip")}>
                                <Icon color="secondary" sx={{fontSize:"14px !important", verticalAlign: "middle"}}>info_outlined</Icon>
                            </MDTooltip>}
                        </MDTypography>
                        <FilterDrawer feature="basket_analysis"/>
                    </Grid>
                </Grid>
                </Card>
            </MDBox>
            <MDBox mt={1.5} mb={15}>
                {loader && <DashboardLoader />}
                {!loader && isConsolidateViewBlock && <ConsolidateBlock/>}
                {!loader && !isConsolidateViewBlock && <Grid container spacing={3}>
                    <Grid item xs={12} md={12} lg={12}>
                        <Element name="section-basket-analysis">
                            <CartAnalysis carts={cartBundles} breakdown={breakdown} />
                            <MDBox mb={10} />
                        </Element>
                    </Grid>
                    </Grid>}
            </MDBox>
        <ReviewBar/>
        <Footer />
        </DashboardLayout>
    );
}

export default ProductCartAnalysis;