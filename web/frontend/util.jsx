import numeral from 'numeral';
import <PERSON>ero from "dinero.js";

export const getMetricFormatterFn = (format_type) => {
    return (val, currency = "") => {
      if (format_type == "currency") {
        return moneyFormatter(val, currency)
      } else if (format_type == "roas") {
        return numeral(val).format('0.0a') + "x"
      } else if (format_type == "percent") {
        return numeral(val).format('0.[00]a') + '%'
      } else if (format_type == "absolute") {
        return numeral(val).format('0,0')
      } else {
        return numeral(val).format('0.[00]a')
      }
    }
}

export function moneyFormatter(val, curr = "") {
    if (curr == "") {
        return numeral(val).format("0.[00]a")
    }

    if (curr == "INR") {
        return rupeeShort(val)
    } else {
        let shortFormat = numeral(val).format("0.[00]a")
        return Dinero({amount : 0, currency: curr}).toFormat("$0").replace("0", shortFormat)
    }
}

export function getColors(l) {
    var baseColors = [
        // mui colors 
        "#e91e63", "#7b809a", "#1A73E8", "#4CAF50", "#fb8c00", "#F44335", "#344767",
        "#f24272", "#d51453", "#f28488", "#e04e3a", "#5f656e", "#a2a6ab", "#69717f", "#c0c4c9", "#006ee3", "#3c85f4", "#4197ff", "#0d5eb6", "#388e3c", "#6ac259", "#7ed370", "#30a945", "#ffb232", "#f7a212", "#fca531", "#ffa828", "#f5594a", "#f32f29", "#f66960", "#f2382f",
        "#283852", "#202b46", "#405780", "#1f2d4b", "#e6e6e6", "#f5f5f5", "#cccccc", "#707070", "#000000", "#ffffff", "#ffd54f", "#ffc107", "#ffeb3b", "#ffff00", "#c0c000", "#008000", "#40a9ff", "#00bfff", "#00ffff", "#c0c0c0",
        "#808080", "#d3d3d3", "#fafafa", "#f0f0f0", "#ececec", "#2ecc71", "#00ff00",

        "#8884d8",
        "#82ca9d",
        "#eda16c",
        "#d43d51",
        "#97bd88",
        "#22f0e6",
        "#e30f91",
        "#7337e0",
        "#5c0543",
        "#5ae94c",
        "#184a20",
        "#73313b",
        "#52fb2c",
        "#9250e8",
        "#4f448c",
        "#a58d62",
        "#ffc658",
        "#c97add",
        "#748081",
        "#359cba",
        "#ec8596",
        "#e2f4f9",
        "#1a9b0a",
        "#c7e52b",
    ];

    while (l > baseColors.length) {
        baseColors.push(
            "#" + (0x1000000 + Math.random() * 0xffffff).toString(16).substr(1, 6)
        );
    }

    return baseColors;
};


export function rupeeShort(value) {

    if (isNaN(value)) {
        return value
    }

    var currencySymbol = '₹';
    if (value == null) {
        return '';
    }
    var InrRSOut = value;
    InrRSOut = Math.round(InrRSOut);
    var RV = "";
    if (InrRSOut > 0 && InrRSOut < 1000) {
        RV = InrRSOut.toString();
    }
    else if (InrRSOut >= 1000 && InrRSOut < 10000) {
        RV = InrRSOut.toString();
    }
    else if (InrRSOut >= 10000 && InrRSOut < 100000) {
        var f1 = InrRSOut.toString().substring(0, 2);
        var f2 = InrRSOut.toString().substring(2, 5);
        RV = f1 + "," + f2;

    }
    else if (InrRSOut >= 100000 && InrRSOut < 1000000) {
        var f1 = InrRSOut.toString().substring(0, 1);
        var f2 = InrRSOut.toString().substring(1, 3);
        if (f2 == "00") {
            RV = f1 + " Lacs";
        }
        else {
            RV = f1 + "." + f2 + " Lacs";
        }
    }
    else if (InrRSOut >= 1000000 && InrRSOut < 10000000) {
        var f1 = InrRSOut.toString().substring(0, 2);
        var f2 = InrRSOut.toString().substring(2, 4);
        if (f2 == "00") {
            RV = f1 + " Lacs";
        }
        else {
            RV = f1 + "." + f2 + " Lacs";
        }
    }
    else if (InrRSOut >= 10000000 && InrRSOut < 100000000) {
        var f1 = InrRSOut.toString().substring(0, 1);
        var f2 = InrRSOut.toString().substring(1, 3);
        if (f2 == "00") {
            RV = f1 + " Cr";
        }
        else {
            RV = f1 + "." + f2 + " Cr";
        }
    }
    else if (InrRSOut >= 100000000 && InrRSOut < 1000000000) {
        var f1 = InrRSOut.toString().substring(0, 2);
        var f2 = InrRSOut.toString().substring(2, 4);
        if (f2 == "00") {
            RV = f1 + " Cr";
        }
        else {
            RV = f1 + "." + f2 + " Cr";
        }
    }
    else if (InrRSOut >= 1000000000 && InrRSOut < 10000000000) {
        var f1 = InrRSOut.toString().substring(0, 3);
        var f2 = InrRSOut.toString().substring(3, 5);
        if (f2 == "00") {
            RV = f1 + " Cr";
        }
        else {
            RV = f1 + "." + f2 + " Cr";
        }
    }
    else if (InrRSOut >= 10000000000) {
        var f1 = InrRSOut.toString().substring(0, 4);
        var f2 = InrRSOut.toString().substring(6, 8);
        if (f2 == "00") {
            RV = f1 + " Cr";
        }
        else {
            RV = f1 + "." + f2 + " Cr";
        }
    }
    else {
        RV = InrRSOut.toString();
    }
    return currencySymbol + RV;
}