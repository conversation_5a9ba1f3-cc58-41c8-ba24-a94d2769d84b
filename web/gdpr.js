import { DeliveryMethod } from "@shopify/shopify-api";
import shopService from './services/shop.js';
import logger from './services/logger.js';
import mysql from "./services/database.js";
import tracker from "./services/tracker.js";
import { updateSubscription, upsertSubscription, getActiveSubscription } from "./services/subscription.js";
import { shopifyBillingConfig } from "./services/subscription/plans.js";

import { createRequire } from "module";
const require = createRequire(import.meta.url);
const dayjs = require("dayjs");
var utc = require('dayjs/plugin/utc')
dayjs.extend(utc)


async function saveWebhookRequest(topic, shop, body, webhookId) {
  logger.info(`received webhook - topic: ${topic}, domain: ${shop}, webhookId: ${webhookId}`)
  var webhook_request = {
      webhook_id : webhookId,
      hmac : '',
      domain : shop ?? '',
      topic : topic ?? 'none',
      verified : 1, 
      payload : body ?? "{}"
  }

  try {
      await mysql.query("INSERT INTO webhook_requests SET ?", webhook_request);
      return true;
  } catch (err) {
      logger.error('error in webhook saveRequest ', err);
      return false
  }
}


var handleAppUninstalled = async (topic, shop, body, webhookId) => {
  await saveWebhookRequest(topic, shop, body, webhookId)

  const payload = JSON.parse(body);
  var shop_id = payload.id ?? '';

  if (!!shop_id) {
      // cancel the current active subscription - for record keeping
      const subscription = await getActiveSubscription(shop_id)
      if (subscription && subscription.subscription_id) {
          await updateSubscription(subscription.subscription_id, "UNINSTALLED", shop_id)
          await shopService.slackAlert(`Subscription Update: ${shop ?? ""} - Cancelled`, '#customer-engagement')
      }
      // save uninstall event in shop tables
      await shopService.afterUninstall(shop ?? '', shop_id)
  }


  var shopOrigin = shop ?? '';
  if (!!shopOrigin) {
      await shopService.updateIntercomUser(shopOrigin, {
          active_flag: 0,
          uninstalled_at : dayjs.utc().utcOffset(330).format('YYYY-MM-DD HH:mm:ss'),
          subscription: "UNINSTALLED"
      })

      tracker.track(shopOrigin, 'Shop Uninstalled')

      await shopService.clearShopCache(shopOrigin, shop_id)
      await shopService.slackAlert(`Uninstalled: ${shopOrigin ?? ""}`, '#customer-engagement')
  }
}


var handleAppSubscriptionsUpdate = async (topic, shop, body, webhookId) => {
  await saveWebhookRequest(topic, shop, body, webhookId)

  const payload = JSON.parse(body);
  let shopObj = await shopService.getShopByOrigin(shop)

  logger.info('handleAppSubscriptionsUpdate', {payload, shop})

  if (!shopObj) {
      logger.error('shop not found for webhook', shop)
      return
  }

  let trialDays = 0;
  let amount = 0;
  let planType = "";
  if (payload.app_subscription.name in shopifyBillingConfig) {
    amount = shopifyBillingConfig[payload.app_subscription.name].amount ?? 0
    trialDays = shopifyBillingConfig[payload.app_subscription.name].trialDays ?? 0
    planType = shopifyBillingConfig[payload.app_subscription.name].planType ?? ""
  }

  let subsData = {
    shop_id : payload.shop_id ?? shopObj.shop_id ?? '',
    subscription_id : payload.app_subscription.admin_graphql_api_id ?? '',
    name : payload.app_subscription.name ?? '',
    plan_type : planType,
    amount : amount,
    trial_days : trialDays,
    status : payload.app_subscription.status ?? ''
  }

  let done = await upsertSubscription(subsData)
  await shopService.slackAlert(`Subscription Update: ${shopObj.myshopify_domain ?? ""} - ${payload.app_subscription.name ?? ""} - ${payload.app_subscription.status}`, '#customer-engagement')

  if (payload.app_subscription.status !== "EXPIRED") {
    let customAttr = {
      subscription: payload.app_subscription.status ?? ''
    }

    if (payload.app_subscription.status === "ACTIVE") {
      customAttr["subscribed_at"] = dayjs.utc().utcOffset(330).format('YYYY-MM-DD HH:mm:ss')
    }

    await shopService.updateIntercomUser(shop, customAttr);
    tracker.track(shop, `Subscription ${payload.app_subscription.status}`)
  }

  logger.info('upsertSubscription', {subsData, done})
}

export default {

  //https://shopify.dev/api/admin-graphql/2023-01/enums/WebhookSubscriptionTopic

  APP_UNINSTALLED : {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/api/webhooks",
    callback: handleAppUninstalled
  },

  APP_SUBSCRIPTIONS_UPDATE : {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/api/webhooks",
    callback: handleAppSubscriptionsUpdate
  },

  /**
   * Customers can request their data from a store owner. When this happens,
   * Shopify invokes this webhook.
   *
   * https://shopify.dev/apps/webhooks/configuration/mandatory-webhooks#customers-data_request
   */
  CUSTOMERS_DATA_REQUEST: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/api/webhooks",
    callback: async (topic, shop, body, webhookId) => {
      const payload = JSON.parse(body);
      await saveWebhookRequest(topic, shop, body, webhookId)
      // Payload has the following shape:
      // {
      //   "shop_id": 954889,
      //   "shop_domain": "{shop}.myshopify.com",
      //   "orders_requested": [
      //     299938,
      //     280263,
      //     220458
      //   ],
      //   "customer": {
      //     "id": 191167,
      //     "email": "<EMAIL>",
      //     "phone": "************"
      //   },
      //   "data_request": {
      //     "id": 9999
      //   }
      // }
    },
  },

  /**
   * Store owners can request that data is deleted on behalf of a customer. When
   * this happens, Shopify invokes this webhook.
   *
   * https://shopify.dev/apps/webhooks/configuration/mandatory-webhooks#customers-redact
   */
  CUSTOMERS_REDACT: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/api/webhooks",
    callback: async (topic, shop, body, webhookId) => {
      const payload = JSON.parse(body);
      await saveWebhookRequest(topic, shop, body, webhookId)
      // Payload has the following shape:
      // {
      //   "shop_id": 954889,
      //   "shop_domain": "{shop}.myshopify.com",
      //   "customer": {
      //     "id": 191167,
      //     "email": "<EMAIL>",
      //     "phone": "************"
      //   },
      //   "orders_to_redact": [
      //     299938,
      //     280263,
      //     220458
      //   ]
      // }
    },
  },

  /**
   * 48 hours after a store owner uninstalls your app, Shopify invokes this
   * webhook.
   *
   * https://shopify.dev/apps/webhooks/configuration/mandatory-webhooks#shop-redact
   */
  SHOP_REDACT: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/api/webhooks",
    callback: async (topic, shop, body, webhookId) => {
      const payload = JSON.parse(body);
      await saveWebhookRequest(topic, shop, body, webhookId)
      // Payload has the following shape:
      // {
      //   "shop_id": 954889,
      //   "shop_domain": "{shop}.myshopify.com"
      // }
    },
  },
};
