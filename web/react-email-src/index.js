import { render } from '@react-email/render';
import * as React from 'react';
import WeeklyReport from './emails/weekly-report.jsx';
import VerificationEmail from './emails/email-verification.jsx';


async function weeklyReportRender(props) {
    return render(WeeklyReport(props))
}

async function verificationEmailRender(props) {
    return render(VerificationEmail(props))
}

export { weeklyReportRender, verificationEmailRender }

