import { createRequire } from "module";
const require = createRequire(import.meta.url);
import shop from "../shop.js";
import logger from '../logger.js';
import dayjs from 'dayjs';
import {
  CLOUD_AIRBYTE_WORKSPACE_ID,
  SOURCE_FB,
  CLOUD_BIGQUERY_DESTINATION_ID,
  CONNECTION_CONFIG_FB_DEFAULT,
} from "./constants.js";


const generateToken = async function () {
    try {
        const sdk = require('api')('@airbyte-api/v1.1#4npt92plyhouh6i');
        let response = await sdk.createAccessToken({
            client_id: process.env.AIRBYTE_CLIENT_ID,
            client_secret: process.env.AIRBYTE_CLIENT_SECRET,
            'grant-type': 'client_credentials'
        })

        if (response.status == 200 && 'data' in response) {
            return response.data?.access_token ?? ""
        }
        logger.error("cloud.generateToken error")
        return ""
    } catch (err) {
        logger.error(`cloud.generateToken error: ` , {
            message: err.message
        })
        return ""
    }
}

const triggerConnectionSync = async function (connection_id) {
    try {
        const sdk = require('api')('@airbyte-api/v1.1#dld83bfloywkuu9');
        sdk.auth(await generateToken());

        let response = await sdk.createJob({
            jobType: 'sync',
            connectionId: connection_id
        })

        if (response.status == 200 && 'data' in response) {
            return true
        }
        logger.error("cloud.triggerCloudSync error", {connection_id})
        return false
    } catch (err) {
        logger.error(`cloud.triggerCloudSync error: ` , {
            connection_id,
            message: err.message
        })
        return false
    }
}

const markConnectionInactive = async function (connection_id) {
    try {
        const sdk = require('api')('@airbyte-api/v1.1#9e8jj3mi8m4d1rbgy');
        sdk.auth(await generateToken());
        let response = await sdk.patchConnection({status: 'inactive'}, {connectionId: connection_id})
        if (response.status == 200) {
            return true
        }
        logger.error("cloud.markConnectionInactive error", {connection_id})
        return false
    } catch (err) {
        logger.error(`cloud.markConnectionInactive error:`, {
            connection_id,
            message: err.message
        })
        return false
    }
}


const getConnectionConfig = (source_type) => {
    if (source_type == SOURCE_FB) {
        return CONNECTION_CONFIG_FB_DEFAULT;
    } else {
        logger.error("cloud.getConnectionConfig Invalid Cloud source type", {source_type});
        return {};
    }
}

// decides the destination dataset for a source_type
const getConnectionNamespace = (source_type, auto_id) => {
    if (source_type === SOURCE_FB) {
        return {
            namespaceDefinition: 'destination',
            namespaceFormat: null
        }
    } else {
        logger.error("cloud.getConnectionConfig Invalid Cloud source type for connection namespace", {source_type});
        return {
            namespaceDefinition: 'destination',
            namespaceFormat: null
        };
    }
}

const createConnection = async function (intg) {

    const {auto_id, request_id, source_type, airbyte_source_id, airbyte_destination_id} = intg;

    let payload = {
        ...getConnectionNamespace(source_type, auto_id),
        schedule: {scheduleType: 'cron', cronExpression: '0 0 12 * * ?'},
        dataResidency: 'auto',
        namespaceDefinition: 'destination',
        namespaceFormat: null,
        nonBreakingSchemaUpdatesBehavior: "propagate_columns",
        sourceId: airbyte_source_id,
        destinationId: airbyte_destination_id,
        status: 'active',
        configurations : getConnectionConfig(source_type)
    }

    try {
        const sdk = require('api')('@airbyte-api/v1.1#9e8jj3mi8m4d1rbgy');
        sdk.auth(await generateToken());
        let response = await sdk.createConnection(payload)
        if (response.status == 200 && 'data' in response) {
            return {
                connection_id: response.data?.connectionId ?? ""
            }
        }
        logger.error("cloud.createConnection error", {source_type, airbyte_source_id, airbyte_destination_id, request_id})
        return {error: 'Failed to create connection'};
    } catch (err) {
        logger.error(`cloud.createConnection error:`, {
            request_id,
            source_type,
            airbyte_source_id,
            airbyte_destination_id,
            message: err.message
        })
        return {error: err.message};
    }
}

async function generateSourceName(auto_id, source_type, shop_id) {
    const { myshopify_domain } = await shop.getShopById(shop_id);
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const formattedDate = `${year}${month}${day}`;
    return `${auto_id}_${formattedDate}_${source_type}_${myshopify_domain.replace(".myshopify.com", "")}`;
}

const createSource = async function (intg) {

    let {
        auto_id,
        request_id,
        secret_id,
        source_type,
        source_config,
        shop_id,
    } = intg;

    let parsed_source_config;
    try {
        parsed_source_config = JSON.parse(source_config);
    } catch (err) {
        logger.error('cloud.createSource: error parsing source_config', { err: err.message, source_config, request_id });
        return {};
    }


    const source_config_id = parsed_source_config.ids?.[0] ?? "";
    if (!source_config_id) {
        logger.error("cloud.createSource Missing source_config_id", {source_config_id, request_id});
        return {}
    }

    const sourceName = await generateSourceName(auto_id, source_type, shop_id);
    let payload = {
        secretId: secret_id,
        name: sourceName,
        workspaceId: CLOUD_AIRBYTE_WORKSPACE_ID
    }

    if (source_type == SOURCE_FB) {

        if (!source_config_id) {
            logger.error("cloud.createSource Missing source_config_id", {source_config_id, request_id});
            return {}
        }

        payload.configuration = {
            // last 1 year data with format ->'2023-01-01T00:00:00Z'
            start_date: dayjs().subtract(1, 'year').startOf("month").format('YYYY-MM-DDT00:00:00') + 'Z',
            account_ids: [source_config_id],
            include_deleted: false,
            fetch_thumbnail_images: true,
            page_size: 100,
            insights_lookback_window: 28,
            max_batch_size: 50,
            action_breakdowns_allow_empty: true,
            sourceType: SOURCE_FB,
            credentials :  {
                auth_type: "Client",
            },
        }
    } else {
        logger.error("cloud.createSource Invalid Cloud source type", {source_type, request_id});
        return {}
    }

    try {
        const sdk = require('api')('@airbyte-api/v1.1#2kj9j2cm1icku4c');
        sdk.auth(await generateToken());
        let response = await sdk.createSource(payload)
        if (response.status == 200 && 'data' in response) {
            return {
                source_id: response.data?.sourceId ?? ""
            }
        }
        logger.error("cloud.createSource error", {source_type, request_id})
        return {}
    } catch (err) {
        logger.error(`cloud.createSource error:` , {
            request_id,
            source_type,
            message: err.message
        })
        return {}
    }
}

const getLatestJobs = async function (connection_id, status = '', limit = 1) {    
    try {
        const sdk = require('api')('@airbyte-api/v1.1#dld83bfloywkuu9');
        sdk.auth(await generateToken());
        let params = {
            connectionId: connection_id,
            limit: limit,
            offset: "0",
            orderBy: "createdAt|DESC",
        }
        if (status) {
            params.status = status;
        }
        let response = await sdk.listJobs(params);

        if (response.status == 200 && response.data && response.data.data && response.data.data.length > 0) {
            return response.data.data
        }
        logger.error(`cloud.getLatestJobs error: failed to get latest jobs`, {connection_id})
        return []
    } catch (err) {
        logger.error(`cloud.getLatestJobs error: ` , {
            connection_id,
            message: err.message
        })
        return []
    }
}

const getWorkspaceId = () => {
    return CLOUD_AIRBYTE_WORKSPACE_ID;
}

const getDestinationId = (source_type) => {
    if (source_type == SOURCE_FB) {
        return CLOUD_BIGQUERY_DESTINATION_ID;
    } else {
        logger.error("cloud.getDestinationId Invalid Cloud source type", {source_type});
        return "";
    }
}

async function handleConnect(source_type, request_id, shop_id) {
    try {
        const sdk = require('api')('@airbyte-api/v1.1#2kj9j2cm1icku4c');
        sdk.auth(await generateToken());
        let response = await sdk.initiateOAuth({
            name: source_type,
            redirectUrl: process.env.HOST + '/api/integrations/authover/' + request_id,
            workspaceId: CLOUD_AIRBYTE_WORKSPACE_ID
        });
    
        if ('status' in response && response.status == 200) {
            return {url: response.data.consentUrl};
        }

        return {error: 'Failed to initiate OAuth'};
    } catch (err) {
        logger.error(`cloud.handleConnect error: `, {
            source_type,
            request_id,
            message: err.message
        })
        return {error: err.message};
    }
}

export default {
    getLatestJobs,
    handleConnect,
    createSource,
    markConnectionInactive,
    triggerConnectionSync,
    createConnection,
    getWorkspaceId,
    getDestinationId
}