import { createRequire } from "module";
const require = createRequire(import.meta.url);
// Import the Google Cloud client library
const {BigQuery} = require('@google-cloud/bigquery');
import logger from "../logger.js";
import shop from "../shop.js";
import { cache } from "../redis.js";
import {DBT_PROD_DATASET_OSS} from "../dbt/index.js";

const bigquery = new BigQuery();

// Important: every complementary source table has same schema
async function getBQSources(shop_id, source_names = []) {
    let useMigrationDataset = await shop.shouldUseMigrationDataset(shop_id);
    let location = useMigrationDataset ? 'eu' : 'us-central1';
    let sources = {}

    for (let source_name of source_names) {
        switch (source_name) {
            case 'cohort_orders':
                sources[source_name] = useMigrationDataset ? `${DBT_PROD_DATASET_OSS}.cohort_orders` : 'processed.cohort_orders';
                break;
            default:
                sources[source_name] = useMigrationDataset ? `${DBT_PROD_DATASET_OSS}.${source_name}` : `processed.${source_name}`;
                break;
        }
    }

    return {sources, location}
}

async function execute(query, location = "us-central1", params = null, labels = {}) {

    try {
        // For all options, see https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/query
        const options = {
            query,
            location,
            labels : Object.assign({
                source : 'dashr-cloud-run'
            }, labels)
        };

        if (!!params) {
            options.params = params
        }

        // Run the query as a job
        const [job] = await bigquery.createQueryJob(options);
        logger.info(`Job ${job.id} started.`, {params : params});
        const [rows] = await job.getQueryResults();
        if (!!rows && rows.length > 0) {
            return rows
        }
        return []
    } catch (error) {
        logger.error('bigquery.execute error - ', error)
        return []
    }
}

async function getFacebookAccount(account_id) {
    let key = `account_v2_${account_id}`
    let account = await cache.get(key)
    if (!!account) {
        return account
    }

    let query = `
        SELECT
            name,
            account_id
        FROM airbyte_v2.ad_account a
        WHERE account_id = @account_id
        GROUP BY 1, 2
        ORDER BY 1 ASC
        LIMIT 1
    `;

    let rows = await execute(query, 'us', {
        account_id,
    });

    logger.info('getFacebookAccount.query', {count : rows.length})

    if (rows.length == 0) {
        return {}
    }

    let response = {
        name : rows[0].name,
        account_id : rows[0].account_id
    }

    await cache.set(key, response)
    return response
}

async function productTypeRepurchases(shop_id, start_date, end_date, repurchase_window)  {


    let {sources, location} = await getBQSources(shop_id, ['cohort_orders']);

    let query = `
    WITH base AS (
        SELECT
          order_id,
          customer_id,
          product_info.title,
          IF (product_info.product_type = '', "Uncategorized", product_info.product_type) as product_type,
          product_info.product_id,
          product_info.image_src,
          total_price,
          order_created_at,
          first_order_at,
          IF(order_id = first_order_id, true, false) as is_first_order
        FROM ${sources.cohort_orders} c,
          c.product_info as product_info
        WHERE ${await shopIdsFilterPart(shop_id)}
            AND order_created_at >= @start_date
            AND first_order_at >= @start_date
            AND first_order_at <= @end_date 
        GROUP BY 1,2,3,4,5,6,7,8,9,10
    ),
    first_orders AS (
        SELECT 
            order_id,
            customer_id,
            product_type,
            total_price
        FROM base 
        WHERE is_first_order = true
    ),
    repeat_orders AS (
        SELECT
            order_id,
            customer_id,
            product_type,
            total_price,
            IF (DATE_DIFF(order_created_at, first_order_at, DAY) <= @repurchase_window, true, false) as is_repurchase,
            IF (DATE_DIFF(order_created_at, first_order_at, DAY) <= 90, total_price, 0) as rev_90d,
            IF (DATE_DIFF(order_created_at, first_order_at, DAY) <= 180, total_price, 0) as rev_180d
        FROM base 
        WHERE is_first_order = false
    ),
    first_order_product_types AS (
        SELECT product_type,
            SUM(total_price) as total_price,
            COUNT(DISTINCT order_id) as order_count,
            COUNT(DISTINCT customer_id) as customer_count,
            ROUND(SAFE_DIVIDE(SUM(total_price), COUNT(DISTINCT order_id))) as aov
        FROM base 
        WHERE is_first_order = true
        GROUP BY 1
    )
    SELECT
        fop.product_type,
        fop.customer_count,
        fop.aov,
        fop.total_price,
        ROUND(SAFE_DIVIDE(fop.total_price + SUM(r.rev_90d), fop.customer_count)) as ltv_90d,
        ROUND(SAFE_DIVIDE(fop.total_price + SUM(r.rev_180d), fop.customer_count)) as ltv_180d,
        ROUND(SAFE_DIVIDE(fop.total_price + SUM(r.total_price), fop.customer_count)) as ltv,
        COUNT(DISTINCT IF(r.is_repurchase, r.customer_id, NULL)) as repeat_customer_count,
        ROUND(100*SAFE_DIVIDE(COUNT(DISTINCT IF(r.is_repurchase, r.customer_id, NULL)), fop.customer_count), 2) as repeat_percentage,
        COUNT(DISTINCT IF(r.product_type = fo.product_type AND r.is_repurchase, fo.customer_id, NULL)) as same_product_repeat_customer_count,
        ROUND(100*SAFE_DIVIDE(COUNT(DISTINCT IF(r.product_type = fo.product_type AND r.is_repurchase, fo.customer_id, NULL)), fop.customer_count), 2) as same_product_repeat_percentage
    FROM first_orders fo
    INNER JOIN first_order_product_types fop on fop.product_type = fo.product_type
    LEFT JOIN repeat_orders r ON fo.customer_id = r.customer_id
    GROUP BY 1,2,3,4
    ORDER BY customer_count desc`;

    let rows = await execute(query, location, {
        start_date,
        end_date,
        repurchase_window,
    });

    logger.info('productTypeRepurchases.query', {count : rows.length})
    return rows;
}


async function productRepurchases(shop_id, start_date, end_date, repurchase_window)  {

    let {sources, location} = await getBQSources(shop_id, ['cohort_orders']);

    let query = `
    WITH base AS (
        SELECT 
          order_id,
          customer_id,
          product_info.title,
          IF (product_info.product_type = '', "Uncategorized", product_info.product_type) as product_type,
          product_info.product_id,
          product_info.image_src,
          total_price,
          order_created_at,
          first_order_at,
          IF(order_id = first_order_id, true, false) as is_first_order
        FROM ${sources.cohort_orders} c,
          c.product_info as product_info
        WHERE ${await shopIdsFilterPart(shop_id)}
            AND order_created_at >= @start_date
            AND first_order_at >= @start_date
            AND first_order_at <= @end_date 
            AND product_info.title != ''
        GROUP BY 1,2,3,4,5,6,7,8,9,10
    ),
    first_orders AS (
        SELECT 
            order_id,
            customer_id,
            product_id,
            total_price
        FROM base 
        WHERE is_first_order = true
    ),
    repeat_orders AS (
        SELECT
            order_id,
            customer_id,
            product_id,
            total_price,
            IF (DATE_DIFF(order_created_at, first_order_at, DAY) <= @repurchase_window, true, false) as is_repurchase,
            IF (DATE_DIFF(order_created_at, first_order_at, DAY) <= 90, total_price, 0) as rev_90d,
            IF (DATE_DIFF(order_created_at, first_order_at, DAY) <= 180, total_price, 0) as rev_180d
        FROM base 
        WHERE is_first_order = false
    ),
    first_order_products AS (
        SELECT product_id,
            title as product_name,
            product_type,
            image_src as image,
            SUM(total_price) as total_price,
            COUNT(DISTINCT order_id) as order_count,
            COUNT(DISTINCT customer_id) as customer_count,
            ROUND(SAFE_DIVIDE(SUM(total_price), COUNT(DISTINCT order_id))) as aov
        FROM base 
        WHERE is_first_order = true
        GROUP BY 1,2,3,4
    )
    SELECT
        fop.product_id,
        fop.product_name,
        fop.product_type,
        fop.image,
        fop.customer_count,
        fop.aov,
        fop.total_price,
        ROUND(SAFE_DIVIDE(fop.total_price + SUM(r.rev_90d), fop.customer_count)) as ltv_90d,
        ROUND(SAFE_DIVIDE(fop.total_price + SUM(r.rev_180d), fop.customer_count)) as ltv_180d,
        ROUND(SAFE_DIVIDE(fop.total_price + SUM(r.total_price), fop.customer_count)) as ltv,
        COUNT(DISTINCT IF(r.is_repurchase, r.customer_id, NULL)) as repeat_customer_count,
        ROUND(100*SAFE_DIVIDE(COUNT(DISTINCT IF(r.is_repurchase, r.customer_id, NULL)), fop.customer_count), 2) as repeat_percentage,
        COUNT(DISTINCT IF(r.product_id = fo.product_id AND r.is_repurchase, fo.customer_id, NULL)) as same_product_repeat_customer_count,
        ROUND(100*SAFE_DIVIDE(COUNT(DISTINCT IF(r.product_id = fo.product_id AND r.is_repurchase, fo.customer_id, NULL)), fop.customer_count), 2) as same_product_repeat_percentage
    FROM first_orders fo
    INNER JOIN first_order_products fop on fop.product_id = fo.product_id
    LEFT JOIN repeat_orders r ON fo.customer_id = r.customer_id
    GROUP BY 1,2,3,4, 5, 6, 7
    ORDER BY customer_count desc`;

    let rows = await execute(query, location, {
        start_date,
        end_date,
        repurchase_window
    });

    logger.info('productRepurchases.query', {count : rows.length})
    return rows;
}

async function cartAnalysis(shop_id, start_date, end_date, combination_size = 2, basket_size = "any", breakdown = 'product_title', applied_filters = {})  {

    let combinationsQueryPart = () => {

        let productFields = '';
        let priceFields = '';
        let crossJoinParts = '';
        let breakdownFields = '';
        let whereParts = '';
        for (let i = 1; i <= combination_size; i++) {
            productFields += `p${i}, `;
            breakdownFields += `p${i}.${breakdownPart}, `;
            priceFields += `p${i}.price + `;
            crossJoinParts += `CROSS JOIN UNNEST(b.product_info) p${i} WITH OFFSET pos${i} `;
            whereParts += `AND p${i}.${breakdownPart} != '' `;
            if (i > 1) {
                whereParts += `AND pos${i - 1} < pos${i} `;
            }
        }

        return (`
            SELECT
                b.order_id,
                b.total_price,
                b.is_first_order,
                ARRAY((SELECT p from UNNEST([${productFields.slice(0, -2)}]) as p order by p.${breakdownPart} desc)) AS products,
                ROUND(${priceFields.slice(0, -3)}, 2) AS combination_price,
                (SELECT COUNT(DISTINCT x) FROM UNNEST([${breakdownFields.slice(0, -2)}]) AS x) as unique_combination_count
            FROM base_filtered b
            ${crossJoinParts}
            WHERE true ${whereParts}
        `);
    }

    let valid_breakdowns = [
        'product_type',
        'product_title',
        'product_vendor',
        'sku'
    ];

    if (!valid_breakdowns.includes(breakdown)) {
        return []
    }

    let breakdownPart = breakdown;
    if (breakdown == 'product_title' || breakdown == 'product_vendor') {
        breakdownPart = breakdown.replace('product_', '')
    }

    let {sources, location} = await getBQSources(shop_id, ['cohort_orders']);

    let query = `
        WITH base AS (
            SELECT 
                c.*,
                IF(order_id = first_order_id, TRUE, FALSE) AS is_first_order
            FROM 
                ${sources.cohort_orders} c
            WHERE ${await shopIdsFilterPart(shop_id)}
                AND order_created_at >= @start_date
                AND order_created_at <= @end_date
                AND ARRAY_LENGTH(product_info) ${basket_size == "exact" ? ` = ${combination_size}` : ' > 1'}
        ),
        order_counts AS (
            SELECT
                COUNT(DISTINCT order_id) AS total_orders,
                COUNT(DISTINCT IF(is_first_order, order_id, NULL)) AS total_new_orders,
                SUM(total_price) as total_sales,
                SUM(IF(is_first_order, total_price, 0)) as total_new_sales
            FROM base
        ),
        base_filtered AS (
            SELECT *
            FROM base
            WHERE ARRAY_LENGTH(${breakdown}) > ${combination_size - 1}
                ${await filterPartsSubqueryBase(applied_filters, false)}
        ),
        combinations AS ( 
            ${combinationsQueryPart()}
        ),
        aggregated_data AS (
            SELECT
                ARRAY_TO_STRING(ARRAY(SELECT ${breakdownPart} FROM UNNEST(products)), ',') AS product_combination,
                ANY_VALUE(products) AS products,
                COUNT(DISTINCT order_id) AS orders,
                COUNT(DISTINCT IF(is_first_order, order_id, NULL)) AS new_orders,
                SUM(combination_price) AS sales,
                SUM(IF(is_first_order, combination_price, 0)) as new_sales
            FROM combinations
            WHERE unique_combination_count = ${combination_size}
            GROUP BY 1
        )
        SELECT 
            product_combination,
            a.products,
            orders,
            ROUND(sales,2) as total_sales,
            ROUND(100*SAFE_DIVIDE(orders, oc.total_orders), 2) AS order_percentage,
            ROUND(SAFE_DIVIDE(sales, orders)) as aov,
            new_orders,
            ROUND(new_sales, 2) as new_total_sales,
            orders - new_orders as repeat_orders,
            ROUND(sales - new_sales, 2) as repeat_total_sales
        FROM  aggregated_data a
        CROSS JOIN order_counts oc
        ORDER BY orders DESC
        LIMIT 1000
    `;

    let rows = await execute(query, location, {
        start_date,
        end_date
    });

    for (let row of rows) {
        row.products = row.products.map(p => {
            return {
                ...p,
                breakdown : p[breakdownPart]
            }
        })
    }

    logger.info('cartAnalysis.query', {count : rows.length})
    return rows;
}

async function cohortBreakdown(
    shop_id,
    start_date,
    end_date,
    time_frame,
    applied_filters,
    breakdown,
    page_number = 1,
    single_product_order = false
) {

    let valid_breakdowns = [
        'product_type',
        'product_title',
        'product_vendor',
        'order_tags',
        'customer_tags',
        'product_tags',
        'sku',
        'shipping_address_country',
        'shipping_address_province',
        'shipping_address_city'
    ];

    if (!valid_breakdowns.includes(breakdown)) {
        return []
    }

    if (breakdown == 'shipping_address_country' || breakdown == 'shipping_address_province' || breakdown == 'shipping_address_city') {
        breakdown = `[${breakdown}]`
    }

    let single_product_filter = '';
    // Applying product filter only in couple of scenarios
    if (breakdown == 'product_title' || breakdown == 'product_type' || breakdown == 'product_vendor' || breakdown == 'sku') {
        single_product_filter = single_product_order ? ' AND product_count = 1 ' : '';
    }

    let {sources, location} = await getBQSources(shop_id, ['cohort_orders']);

    let query = `
        WITH base AS (
            SELECT
                c.*,
                IF(order_id = first_order_id, true, false) as is_first_order,
                DATE_TRUNC(first_order_at, WEEK(MONDAY)) as week,
                DATE_TRUNC(first_order_at, MONTH) as month,
                DATE_TRUNC(first_order_at, QUARTER) as quarter,
                DATE_TRUNC(first_order_at, YEAR) as year,
                FIRST_VALUE(${breakdown}) OVER (PARTITION BY customer_id, shop_id ORDER BY created_at) as first_order_breakdown
            FROM ${sources.cohort_orders} c
            WHERE ${await shopIdsFilterPart(shop_id)}
                AND order_created_at >= @start_date
                AND first_order_at >= @start_date
                AND first_order_at <= @end_date
        ),
        cohort_customers AS (
            SELECT customer_id as fo_customer_id
            FROM base
            WHERE is_first_order = true
                ${single_product_filter}
        ),
        breakdown as (
            SELECT *
            FROM base b, b.first_order_breakdown as cohort
            INNER JOIN cohort_customers c ON c.fo_customer_id = b.customer_id
        ),
        order_cohorts as (
            SELECT *,
            CASE
                WHEN is_first_order THEN "first_order"
                WHEN period = 'week' THEN CONCAT("diff_", DATE_DIFF(order_created_at, first_order_at, WEEK(MONDAY)))
                WHEN period = 'month'THEN CONCAT("diff_", DATE_DIFF(order_created_at, first_order_at, MONTH))
                WHEN period = 'quarter'THEN CONCAT("diff_", DATE_DIFF(order_created_at, first_order_at, QUARTER))
                WHEN period = 'year'THEN CONCAT("diff_", DATE_DIFF(order_created_at, first_order_at, YEAR))
            END AS diff,
            IF(is_first_order, NULL, customer_id) AS repeat_customer_id
            FROM breakdown b
            UNPIVOT (first_order_period FOR period IN (week, month, quarter, year)) as unpivoted
            WHERE order_created_at >= @start_date
                AND order_created_at <= @end_date
                ${await filterPartsSubqueryBase(applied_filters)}
        ),
        enriched_orders AS (
            SELECT c.*,
            COUNT(DISTINCT repeat_customer_id) OVER (PARTITION BY period, cohort) as repeat_customer_count,
            COUNT(DISTINCT order_id) OVER (PARTITION BY period, cohort, customer_id) as order_freq,
            COUNT(DISTINCT cohort) OVER (PARTITION BY period) as cohort_count
            FROM order_cohorts c
        ),
        transaction_frequency AS (
            SELECT period, cohort, ARRAY_AGG(STRUCT(order_freq, cust_count)) as txn_freq
            FROM (
                SELECT period, cohort, IF (order_freq > 20, "20+", CAST(order_freq AS STRING)) as order_freq, COUNT(DISTINCT customer_id) as cust_count 
                FROM enriched_orders
                GROUP BY 1,2, 3
            )
            GROUP BY 1,2
        )
        SELECT e.period,
            e.cohort,
            repeat_customer_count,
            diff as bucket,
            ANY_VALUE(t.txn_freq) as txn_freq,
            COUNT(DISTINCT customer_id) as customer_count,
            COUNT(DISTINCT order_id) as order_count,
            ROUND(SUM(total_price)) as total_price,
            CEIL(SAFE_DIVIDE(ANY_VALUE(cohort_count), 10)) as max_pages
        FROM enriched_orders e
        JOIN transaction_frequency t ON e.period = t.period AND e.cohort = t.cohort
        WHERE e.period = @time_frame
        AND e.cohort IN (
            SELECT cohort
            FROM enriched_orders
            WHERE period = @time_frame
            GROUP BY 1
            ORDER BY COUNT(DISTINCT customer_id) DESC
            LIMIT 10 OFFSET @offset
        )
        GROUP BY 1,2,3,4
        ORDER BY customer_count DESC
    `;

    let rows = await execute(query, location, {
        start_date,
        end_date,
        time_frame,
        offset : 10*(page_number - 1)
    });

    logger.info('cohortBreakdown.query', {count : rows.length})
    return rows;
}


async function filterPartsSubqueryBase(applied_filters, first_order = true) {

    if (!applied_filters || Object.keys(applied_filters).length == 0) {
        return ''
    }

    let filters = [
        "order_tags",
        "customer_tags",
        "product_title",
        "product_tags",
        "product_type",
        "product_vendor"
    ];

    let filter_query_parts = []

    for (let key of filters) {
        if (!(key in applied_filters)) {
            continue;
        }

        for (let filter of applied_filters[key]) {
            let { id, op, values } = filter;

            if (values.length == 0) {
                continue;
            }

            let operator = '';
            let filter_exclusion = false;
            let valuesStr = '';
            let lowerCase = false;
            values = values.map(value => `${value.replace(/"/g, '\\"')}`)
            switch (op) {
                case 'op_is':
                    operator = 'IN';
                    valuesStr = '"' + values.join('","') + '"';
                    break;
                case 'op_is_not':
                    operator = 'IN';
                    filter_exclusion = true;
                    valuesStr = '"' + values.join('","') + '"';
                    break;
                case 'op_contains':
                    operator = 'LIKE';
                    valuesStr = `"%${values[0].toLowerCase()}%"`;
                    lowerCase = true;
                    break;
                case 'op_contains_not':
                    operator = 'LIKE';
                    filter_exclusion = true;
                    valuesStr = `"%${values[0].toLowerCase()}%"`;
                    lowerCase = true;
                    break;
                // case 'starts with':
                //     operator = 'LIKE';
                //     values = values.map(value => `${value}%`);
                //     break;
                // case 'ends with':
                //     operator = 'LIKE';
                //     values = values.map(value => `%${value}`);
                //     break;
            }

            if (!operator || !valuesStr) {
                continue;
            }

            filter_query_parts.push(
                `AND ${filter_exclusion ? 'NOT' : ''} EXISTS(SELECT * FROM UNNEST(${key}) AS x WHERE ${lowerCase ? 'LOWER(x)' : 'x'} ${operator} (${valuesStr}))`
            )
        }
    }

    filters = [
        'source_name',
        'shipping_address_country',
        'shipping_address_province',
        'shipping_address_city'
    ];

    for (let key of filters) {
        if (!(key in applied_filters)) {
            continue;
        }

        for (let filter of applied_filters[key]) {
            let { id, op, values } = filter;

            if (values.length == 0) {
                continue;
            }

            let operator = '';
            let valuesStr = '';
            let lowerCase = false;
            values = values.map(value => `${value.replace(/"/g, '\\"')}`)
            switch (op) {
                case 'op_is':
                    operator = 'IN';
                    valuesStr = '"' + values.join('","') + '"';
                    break;
                case 'op_is_not':
                    operator = 'NOT IN';
                    valuesStr = '"' + values.join('","') + '"';
                    break;
                case 'op_contains':
                    operator = 'LIKE';
                    lowerCase = true;
                    valuesStr = `"%${values[0].toLowerCase()}%"`;
                    break;
                case 'op_contains_not':
                    operator = 'NOT LIKE';
                    lowerCase = true;
                    valuesStr = `"%${values[0].toLowerCase()}%"`;
                    break;
                // case 'starts with':
                //     operator = 'LIKE';
                //     values = values.map(value => `${value}%`);
                //     break;
                // case 'ends with':
                //     operator = 'LIKE';
                //     values = values.map(value => `%${value}`);
                //     break;
            }

            if (!operator || !valuesStr) {
                continue;
            }

            filter_query_parts.push(`AND ${lowerCase ? `LOWER(${key})` : key} ${operator} (${valuesStr})`)
        }
    }

    if (filter_query_parts.length == 0) {
        return ''
    }

    if (!first_order) {
        return ` ${filter_query_parts.join(' ')}`;
    }

    return ` AND customer_id IN (
        SELECT DISTINCT customer_id
        FROM base
        WHERE order_id = first_order_id
            ${filter_query_parts.join(' ')}
    )`;
}

// customerSegmentCustomers & customerSegmentCustomersLegacy are same except
// customerSegmentCustomersLegacy has customers table join and works on legacy dataset
async function customerSegmentCustomers(shop_id, start_date, end_date) {
    let query = `
        WITH base AS (
            SELECT
                customer_id,
                customer_email as email,
                IF(customer_address_phone IS NULL OR customer_address_phone = '', customer_phone, customer_address_phone) as phone,
                customer_first_name as first_name,
                customer_last_name as last_name,
                customer_address_city as city,
                customer_address_province as province,
                customer_address_country_name as country,
                customer_address_zip as zip,
                FORMAT_DATE("%Y-%m-%d", first_order_at) as first_order_date,
                COUNT(DISTINCT order_id) as order_count,
                ROUND(SUM(total_price)) as order_total,
                NTILE(4) OVER (ORDER BY SUM(total_price) DESC) AS quartile
            FROM ${DBT_PROD_DATASET_OSS}.cohort_orders
            WHERE ${await shopIdsFilterPart(shop_id)}
                AND order_created_at >= @start_date
                AND first_order_at >= @start_date
                AND first_order_at <= @end_date
            GROUP BY 1,2,3,4,5,6,7,8,9,10
            ORDER BY order_total DESC
        )
        SELECT b.* ,
            CASE
                WHEN b.quartile = 1 THEN '1st (Top 25%)'
                WHEN b.quartile = 2 THEN '2nd (25% - 50%)'
                WHEN b.quartile = 3 THEN '3rd (50% - 75%)'
                WHEN b.quartile = 5 THEN '4th (Bottom 25%)'
            END AS quartile_name
        FROM base b
        LIMIT 50000
    `;

    let rows = [];
    try {
        rows = await execute(query, "eu", {
            start_date,
            end_date
        });

        logger.info('customerSegmentCustomers.query', {count : rows.length})
    } catch (error) {
        logger.error('customerSegmentCustomers.error', error)
    }

    return rows;
}

async function customerSegmentCustomersLegacy(shop_id, start_date, end_date) {

    let query = `
        WITH base AS (
            SELECT
                customer_id,
                FORMAT_DATE("%Y-%m-%d", first_order_at) as first_order_date,
                COUNT(DISTINCT order_id) as order_count,
                ROUND(SUM(total_price)) as order_total,
                NTILE(4) OVER (ORDER BY SUM(total_price) DESC) AS quartile
            FROM processed.cohort_orders
            WHERE ${await shopIdsFilterPart(shop_id)}
                AND order_created_at >= @start_date
                AND first_order_at >= @start_date
                AND first_order_at <= @end_date
            GROUP BY 1,2
            ORDER BY order_total DESC
        ),

        redacted_customers AS (
            SELECT DISTINCT s.shop_id, JSON_VALUE(payload, "$.customer.id") as customer_id
            FROM mysql.webhook_requests wr
            INNER JOIN mysql.shops s ON wr.domain = s.myshopify_domain
            WHERE topic IN ('CUSTOMERS_REDACT', 'customers/redact')
        ),

        final AS (
            SELECT b.* ,
                CASE
                    WHEN b.quartile = 1 THEN '1st (Top 25%)'
                    WHEN b.quartile = 2 THEN '2nd (25% - 50%)'
                    WHEN b.quartile = 3 THEN '3rd (50% - 75%)'
                    WHEN b.quartile = 5 THEN '4th (Bottom 25%)'
                END AS quartile_name,
                c.email,
                c.phone,
                c.first_name,
                c.last_name
            FROM base b
            INNER JOIN mysql.customers c ON c.customer_id = b.customer_id
                AND ${await shopIdsFilterPart(shop_id)}
                AND c.created_at >= @start_date
        ), 

        final_with_redaction AS (
            SELECT
            f.* EXCEPT(email, phone, first_name, last_name),
            CASE WHEN rc.customer_id IS NOT NULL THEN 'REDACTED' ELSE f.email END AS email,
            CASE WHEN rc.customer_id IS NOT NULL THEN 'REDACTED' ELSE f.phone END AS phone,
            CASE WHEN rc.customer_id IS NOT NULL THEN 'REDACTED' ELSE f.first_name END AS first_name,
            CASE WHEN rc.customer_id IS NOT NULL THEN 'REDACTED' ELSE f.last_name END AS last_name
            FROM final f
            LEFT JOIN redacted_customers rc ON rc.customer_id = f.customer_id
        )

        SELECT * FROM final_with_redaction
        LIMIT 50000
    `;

    let rows = [];
    try {
        rows = await execute(query, "us-central1", {
            start_date,
            end_date
        });

        logger.info('customerSegmentCustomers.query', {count : rows.length})
    } catch (error) {
        logger.error('customerSegmentCustomers.error', error)
    }

    return rows;
}

async function shopIdsFilterPart(shop_id) {

    let shop_ids_str = '';
    if (Array.isArray(shop_id) && shop_id.length > 0) {
        shop_ids_str = '"' + shop_id.join('","') + '"';
    } else {
        shop_ids_str = '"' + shop_id + '"';
    }

    return ` shop_id IN (${shop_ids_str}) `;
}

async function customerSegments(shop_id, start_date, end_date) {

    let {sources, location} = await getBQSources(shop_id, ['cohort_orders']);

    let query = `
        WITH base AS (
            SELECT
                customer_id,
                COUNT(DISTINCT order_id) as order_count,
                SUM(total_price) as order_total,
                NTILE(4) OVER (ORDER BY SUM(total_price) DESC) AS quartile
            FROM ${sources.cohort_orders}
            WHERE ${await shopIdsFilterPart(shop_id)}
                AND order_created_at >= @start_date
                AND first_order_at >= @start_date
                AND first_order_at <= @end_date
            GROUP BY 1
            ORDER BY order_total DESC
        ),
        quartiles AS (
            SELECT quartile, 
                COUNT(DISTINCT customer_id) AS customer_count,
                ROUND(SUM(order_count)) AS order_count,
                ROUND(SUM(order_total)) AS total_revenue,
                ROUND(SAFE_DIVIDE(SUM(order_count), COUNT(DISTINCT customer_id)), 2) AS order_frequency,
                ROUND(SAFE_DIVIDE(SUM(order_total), COUNT(DISTINCT customer_id))) AS ltv
            FROM base
            GROUP BY 1
        ),
        percentiles AS (
            SELECT TO_JSON(approx_quantiles(ROUND(order_total), 100)) AS sales_percentiles
            FROM base
        ),
        transaction_frequency AS (
            SELECT TO_JSON(ARRAY_AGG(STRUCT(order_freq, cust_count))) as txn_freq
            FROM (
                SELECT IF (order_count > 20, "20+", CAST(order_count AS STRING)) as order_freq, COUNT(DISTINCT customer_id) as cust_count 
                FROM base
                GROUP BY 1
            )
        )
        SELECT 'quartiles' as type, TO_JSON(ARRAY_AGG(q)) as data FROM quartiles q
        UNION ALL SELECT 'transaction_frequency' as type, txn_freq as data FROM transaction_frequency
        UNION ALL SELECT 'percentiles' as type, sales_percentiles as data FROM percentiles
    `;


    let result = {};
    try {
        let rows = await execute(query, location, {
            start_date,
            end_date
        });

        for (let row of rows) {
            result[row.type] = []
            try {
                result[row.type] = JSON.parse(row.data)
            } catch (e) {
                logger.error('customerSegments.error JSON.parse', e)
            }
        }
    
        logger.info('customerSegments.query', {count : rows.length})
    } catch (error) {
        logger.error('customerSegments.error', error)
    }

    return result;
}

async function cohortAnalysis(shop_id, start_date, end_date, time_frame, applied_filters) {

    let {sources, location} = await getBQSources(shop_id, ['cohort_orders']);

    let query = `
    WITH base AS (
        SELECT
            c.*,
            IF(order_id = first_order_id, true, false) as is_first_order,
            DATE_TRUNC(first_order_at, WEEK(MONDAY)) as week,
            DATE_TRUNC(first_order_at, MONTH) as month,
            DATE_TRUNC(first_order_at, QUARTER) as quarter,
            DATE_TRUNC(first_order_at, YEAR) as year
        FROM ${sources.cohort_orders} c
        WHERE ${await shopIdsFilterPart(shop_id)}
            AND order_created_at >= @start_date
            AND first_order_at >= @start_date
            AND first_order_at <= @end_date
    ),
    order_cohorts as (
        SELECT *,
        CASE
            WHEN is_first_order THEN -1
            WHEN period = 'week' THEN DATE_DIFF(order_created_at, first_order_at, WEEK(MONDAY))
            WHEN period = 'month'THEN DATE_DIFF(order_created_at, first_order_at, MONTH)
            WHEN period = 'quarter'THEN DATE_DIFF(order_created_at, first_order_at, QUARTER)
            WHEN period = 'year'THEN DATE_DIFF(order_created_at, first_order_at, YEAR)
        END AS diff,
        CASE
            WHEN period = 'week' THEN FORMAT_DATE("%Y-%m-%d", DATE_TRUNC(first_order_period, WEEK(MONDAY)))
            WHEN period = 'month'THEN FORMAT_DATE("%Y-%m", first_order_period)
            WHEN period = 'quarter'THEN FORMAT_DATE("%Y-0%Q", first_order_period)
            WHEN period = 'year'THEN FORMAT_DATE("%Y", first_order_period)
        END AS cohort,
        CASE
            WHEN is_first_order THEN "first_order"
            WHEN period = 'week' THEN FORMAT_DATE("%Y-%m-%d", DATE_TRUNC(order_created_at, WEEK(MONDAY)))
            WHEN period = 'month'THEN FORMAT_DATE("%Y-%m", order_created_at)
            WHEN period = 'quarter'THEN FORMAT_DATE("%Y-0%Q", order_created_at)
            WHEN period = 'year'THEN FORMAT_DATE("%Y", order_created_at)
        END AS order_period,
        IF(is_first_order, NULL, customer_id) AS repeat_customer_id
        FROM base b
        UNPIVOT (first_order_period FOR period IN (week, month, quarter, year)) as unpivoted
        WHERE order_created_at >= @start_date
            AND order_created_at <= @end_date
            ${await filterPartsSubqueryBase(applied_filters)}
    ),
    enriched_orders AS (
      SELECT c.*,
        STRUCT(
            COUNT(DISTINCT customer_id) OVER p as customer_count,
            IFNULL(ROUND(SAFE_DIVIDE(SUM(total_price) OVER p , COUNT(DISTINCT order_id) OVER p)), 0) as aov
        ) as summary,
        COUNT(DISTINCT repeat_customer_id) OVER (PARTITION BY period, cohort) as repeat_customer_count,
        COUNT(DISTINCT order_id) OVER (PARTITION BY period, cohort, customer_id) as order_freq
      FROM order_cohorts c
      WINDOW p AS (PARTITION BY period)
    ),
    transaction_frequency AS (
        SELECT period, cohort, ARRAY_AGG(STRUCT(order_freq, cust_count)) as txn_freq
        FROM (
            SELECT period, cohort, IF (order_freq > 20, "20+", CAST(order_freq AS STRING)) as order_freq, COUNT(DISTINCT customer_id) as cust_count 
            FROM enriched_orders
            GROUP BY 1,2, 3
        )
        GROUP BY 1,2
    ),
    month_cohorts AS (
        SELECT cohort, diff, ROUND(SUM(total_price)) as month_revenue, COUNT(distinct customer_id) as month_customers
        FROM enriched_orders
        WHERE period = 'month'
        GROUP BY 1,2
        ORDER BY cohort, diff
    ),
    diff_range AS (
        SELECT cohort, diff
        FROM (
            SELECT cohort, GENERATE_ARRAY( -1,  DATE_DIFF(DATE_TRUNC(DATE(@end_date), MONTH), PARSE_DATE('%Y-%m', cohort), MONTH)) AS diff_range
            FROM month_cohorts
            GROUP BY cohort
        ), UNNEST(diff_range) AS diff
        ORDER BY 1,2
    ),
    filled_month_cohorts AS (
        SELECT 
            r.cohort,
            r.diff,
            COALESCE(m.month_revenue, 0) AS month_revenue,
            COALESCE(m.month_customers, 0) AS month_customers
        FROM diff_range r
        LEFT JOIN month_cohorts m
        ON r.cohort = m.cohort AND r.diff = m.diff
    ),
    cum_month_cohorts AS (
        SELECT 
            cohort, 
            diff, 
            SUM(month_revenue) OVER (PARTITION BY cohort ORDER BY diff ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) AS cumulative_revenue,
            FIRST_VALUE(month_customers) OVER (PARTITION BY cohort ORDER BY diff ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW) as initial_customers
        FROM 
            filled_month_cohorts
        ORDER BY cohort, diff
    ),
    weighted_avg AS (
        SELECT  diff, ROUND(SAFE_DIVIDE(SUM(cumulative_revenue), SUM(initial_customers))) as ltv
        FROM  cum_month_cohorts
        GROUP BY 1
        ORDER BY 1
    ),
    ltv_summary AS (
        SELECT STRUCT(
            SUM(IF(diff = 1, ltv, 0)) as ltv1m,
            SUM(IF(diff = 3, ltv, 0)) as ltv3m,
            SUM(IF(diff = 6, ltv, 0)) as ltv6m,
            SUM(IF(diff = 12, ltv, 0)) as ltv1y,
            SUM(IF(diff = 24, ltv, 0)) as ltv2y,
            SUM(IF(diff = 36, ltv, 0)) as ltv3y
        ) as ltv
        FROM weighted_avg
    )
    SELECT e.period,
        e.cohort,
        diff,
        repeat_customer_count,
        order_period as bucket,
        ANY_VALUE(ltv) as ltv,
        ANY_VALUE(summary) as summary,
        ANY_VALUE(t.txn_freq) as txn_freq,
        COUNT(DISTINCT customer_id) as customer_count,
        COUNT(DISTINCT order_id) as order_count,
        ROUND(SUM(total_price)) as total_price
    FROM enriched_orders e
    JOIN ltv_summary ls ON TRUE
    JOIN transaction_frequency t ON e.period = t.period AND e.cohort = t.cohort
    WHERE e.period = @time_frame
    GROUP BY 1,2,3,4,5
    ORDER BY 1,2,3
    `;

    let rows = await execute(query, location, {
        start_date,
        end_date,
        time_frame
    });

    logger.info('cohortAnalysis.query', {count : rows.length})
    return rows;
}

export default {
    execute,
    getFacebookAccount,
    cartAnalysis,
    productRepurchases,
    productTypeRepurchases,
    cohortBreakdown,
    cohortAnalysis,
    customerSegments,
    customerSegmentCustomers,
    customerSegmentCustomersLegacy,
}