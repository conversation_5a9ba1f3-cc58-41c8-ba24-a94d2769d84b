
import { createRequire } from "module";
const require = createRequire(import.meta.url);
const dayjs = require("dayjs");
import logger from '../logger.js';
import ltv from '../ltv/index.js';
import {rfmSegmentExport} from '../cube/data.js';

async function getJSONData({ consolidated_shop, type, parameters }) {
    let response = {
        data : {},
        fields : [],
        error : ''
    }

    let shop_id = '';
    let group_shop_ids = [];
    if (Array.isArray(consolidated_shop.shop_id)) {
        group_shop_ids = consolidated_shop.shop_id;
    } else {
        shop_id = consolidated_shop.shop_id;
    }

    switch (type) {
        case 'basic_segments_export':
            let exportResopnse = await ltv.profileJSON(consolidated_shop, parameters.start_date, parameters.end_date);
            response.data = exportResopnse.data ?? {};
            response.fields = exportResopnse.fields ?? [];
            response.error = exportResopnse.error ?? '';
            break;
        case 'rfm_segments_export':
            let exportRFMResopnse = await rfmSegmentExport(shop_id, group_shop_ids, parameters.segment, parameters.period);
            response.data = exportRFMResopnse.data ?? {};
            response.fields = exportRFMResopnse.fields ?? [];
            response.error = exportRFMResopnse.error ?? '';
            break;
    }

    return response;
}


export default {
    getJSONData
};