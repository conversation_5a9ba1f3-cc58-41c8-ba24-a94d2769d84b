import axios from "axios";
import { promisify } from "util";
import { fetchRFMSegmentCustomers } from "../cube/data";
import logger from "../logger";
const sleep = promisify(setTimeout);

const KLAVIYO_API_URL = "https://a.klaviyo.com/api";
const HEADERS = {
  accept: "application/json",
  revision: "2024-05-15",
};
const CHUNK_SIZE = 10000;

async function getAccount(klaviyo_api_key) {
  const url = `${KLAVIYO_API_URL}/accounts/`;
  const options = {
    method: "GET",
    url: url,
    headers: {
      ...HEADERS,
      Authorization: `Klaviyo-API-Key ${klaviyo_api_key}`,
    },
  };

  try {
    const profile = await axios(options);
    return profile;
  } catch (error) {
    if (error.response && error.response.status === 401) {
      logger.error("Invalid API key");
      return {error: "Invalid API key"};
    } else {
      logger.error("Error in fetching data.");
      return {error: "Error in fetching data."};
    }
  }
}

async function testConnection(klaviyo_api_key) {
  const profile = await getAccount(klaviyo_api_key);
  if (profile.error) {
    logger.error("Error in testConnection:", profile.error);
    return false;
  }
  return true;
}

function chunkPayload(payload, chunkSize) {
  const profilesData = payload.data.attributes.profiles.data;
  const chunks = [];
  let index = 0;

  while (index < profilesData.length) {
    chunks.push({
      data: {
        type: "profile-bulk-import-job",
        attributes: {
          profiles: {
            data: profilesData.slice(index, index + chunkSize),
          },
        },
      },
    });
    index += chunkSize;
  }

  return chunks;
}

async function bulkAddToKlaviyo(payloads, klaviyo_api_key) {
  const url = `${KLAVIYO_API_URL}/profile-bulk-import-jobs/`;
  const headers = {
    ...HEADERS,
    Authorization: `Klaviyo-API-Key ${klaviyo_api_key}`,
  };

  const klaviyo_job_ids = [];
  try {
    for (const payload of payloads) {
      const chunkedPayloads = chunkPayload(payload, CHUNK_SIZE);
      for (var k in chunkedPayloads) {
        const chunk = chunkedPayloads[k];
        const options = {
          method: "POST",
          url: url,
          headers: headers,
          data: chunk,
        };

        try {
          const response = await axios(options);
          let klaviyo_job_id = response?.data?.data?.id ?? null;
          logger.info(`bulkAddToKlaviyo: Added profiles to Klaviyo with job id: ${klaviyo_job_id}`);
          klaviyo_job_ids.push(klaviyo_job_id);
        } catch (error) {
          if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            logger.error(`Error in bulkAddToKlaviyo for chunk index ${k}`, {
              errorMessage: error.message,
              response: {
                data: error.response.data,
                status: error.response.status,
                headers: error.response.headers,
              },
            });
          } else if (error.request) {
            // The request was made but no response was received
            logger.error(`Error in bulkAddToKlaviyo for chunk index ${k}`, {
              errorMessage: error.message,
              request: error.request,
            });
          } else {
            // Something happened in setting up the request that triggered an Error
            logger.error(`Error in bulkAddToKlaviyo for chunk index ${k}`, {
              errorMessage: error.message,
            });
          }
          return {error: "Error in bulkAddToKlaviyo"};
        }
      }
      
      await sleep(1000); // Sleep for 1 second after each segment payload
    }
    return {klaviyo_job_ids: klaviyo_job_ids};
  } catch (error) {
    logger.error(`Error in bulkAddToKlaviyo: ${error}`);
    return {error: "Error in bulkAddToKlaviyo"};
  }
}


function isValidEmail(email) {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  if (!emailRegex.test(email)) {
    return false;
  }

  const localPart = email.split('@')[0];
  const isValidLocalPart = /^[a-zA-Z0-9._%+-]+$/.test(localPart) && !/^[-.]/.test(localPart) && !/[.]{2,}/.test(localPart) && !/[.]$/.test(localPart);

  return isValidLocalPart;
}

function prepareRFMSegmentPayload(segmentEmailObject) {
  const payloads = [];
  for (const segment in segmentEmailObject) {

    // Filter out invalid emails
    let emailArray = [];
    let invalidEmailArray = [];
    for (var k in segmentEmailObject[segment]) {
      let email = segmentEmailObject[segment][k];

      if (!isValidEmail(email)) {
        console.log("Invalid email", email);
        invalidEmailArray.push(email);
        continue;
      }
      emailArray.push(email);
    }

    if (invalidEmailArray.length !== 0) {
      logger.warn("Invalid email in RFM segment", {
        segment,
        length : invalidEmailArray.length,
        emails : invalidEmailArray
      });
    }

    if (emailArray.length === 0) {
      logger.warn("No customer in RFM segment", {segment});
      continue;
    }

    const profilesData = emailArray.map((email) => ({
      type: "profile",
      attributes: {
        email: email,
        properties: {
          datadrew_segment: segment,
        },
      },
    }));

    const bulkImportData = {
      data: {
        type: "profile-bulk-import-job",
        attributes: {
          profiles: {
            data: profilesData,
          },
        },
      },
    };

    payloads.push(bulkImportData);
  }

  return payloads;
}

async function syncRFMSegment(shop_id, group_shop_ids, segment, period, klaviyo_api_key) {
  const segmentEmailObject = await fetchRFMSegmentCustomers(
    shop_id,
    group_shop_ids,
    segment,
    period
  );

  if (!segmentEmailObject || Object.keys(segmentEmailObject).length === 0) {
    return {error: "Error fetching RFM Segment Customers"};
  }

  for (const segment in segmentEmailObject) {
    logger.info(`syncRFMSegment : Segment: ${segment}, Customers: ${segmentEmailObject[segment].length}`);
  }

  const payloads = prepareRFMSegmentPayload(segmentEmailObject);

  if (payloads.length === 0) {
    logger.warn("No customer in RFM segment");
    return { error: "No customer in RFM segment" };
  }

  try {
    const responses = await bulkAddToKlaviyo(payloads, klaviyo_api_key);
    if (responses.error) {
      logger.error("Error creating profile bulk import jobs");
      return { error: responses.error };
    }
    return responses;
  } catch (error) {
    logger.error("Error creating profile bulk import jobs:", error);
    return { error: "Error creating profile bulk import jobs"};
  }
}

export default {
  bulkAddToKlaviyo,
  syncRFMSegment,
  testConnection
};
