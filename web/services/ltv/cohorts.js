import { createRequire } from "module";
const require = createRequire(import.meta.url);
const dayjs = require("dayjs");
import util from '../common/util.js';
import logger from '../logger.js';
import {createTimeFrameCohorts, createBreakdownCohorts} from './timeframe.js';
import bigquery from '../bigquery/index.js';
import { isFeatureEnabled } from "../subscription/plans.js";

function getDefaultFrequencyMap(frequency_group) {

    let txn_frequency_map = {};
    for (let x = 1; x <= frequency_group; x++) {
        txn_frequency_map[`${x}txn`] = {
            "bucket_index" : `exactly ${x} transactions`,
            "value" : 0,
            "percentage" : 0
        }
    }
    txn_frequency_map[`${frequency_group}+txn`] = {
        "bucket_index" : `more than ${frequency_group} transactions`,
        "value" : 0,
        "percentage" : 0
    }

    return txn_frequency_map;
}

async function calculateTxnFrequencyOnCohorts(cohorts, frequency_group) {
    var perc_round = (x) => Math.round(x * 1000) / 10
    var div = (x, y) => {
        if (y > 0) {
            return x/y
        }
        return 0
    }

    let xLabels = []
    for (let x = 1; x <= frequency_group; x++) {
        xLabels.push(`${x} txn.`)
    }
    xLabels.push(`${frequency_group}+ txn.`)

    var data = {};

    var frequency = {
        yLabels: [],
        xLabels,
        data : [],
        table : {
            new_customers : []
        },
        table_sum : {
            new_customers : 0
        }
    }

    for (var k in cohorts) {
        var cohort = cohorts[k];

        if (cohort.blur) {
            continue;
        }

        frequency.yLabels.push(cohort.name)
        let txn_frequency_map = getDefaultFrequencyMap(frequency_group)

        for (var ck in cohort.txn_frequency_map) {
            if (`${ck}txn` in txn_frequency_map) {
                txn_frequency_map[`${ck}txn`].value = cohort.txn_frequency_map[ck]
            } else {
                txn_frequency_map[`${frequency_group}+txn`].value += cohort.txn_frequency_map[ck] 
            }
        }

        let frequency_row = []

        for (var cki in txn_frequency_map) {
            txn_frequency_map[cki].percentage = perc_round(div(
                txn_frequency_map[cki].value,
                cohort.first_order_bucket.customer_count
            ))
            txn_frequency_map[cki].display_value = txn_frequency_map[cki].value
            txn_frequency_map[cki].display_percentage = `${txn_frequency_map[cki].percentage}%`
            txn_frequency_map[cki].cohort_name = cohort.name
            txn_frequency_map[cki].cohort_size = cohort.first_order_bucket.customer_count

            let dataRef = `${cohort.dataKey}:${cki}`
            data[dataRef] = txn_frequency_map[cki]
            frequency_row.push(dataRef)
        }

        frequency.table.new_customers.push({
            val : cohort.first_order_bucket.customer_count,
            is_blur : false
        })
        frequency.data.push(frequency_row)
        frequency.table_sum.new_customers += cohort.first_order_bucket.customer_count;
    }

    return {data, frequency}
}

async function getLTVTrends(start_date, end_date, data, consolidated_shop) {
    let summary = {}
    let ltv = {}
    for (var k in data) {
        let row = data[k]
        if (!row.summary || Object.keys(row.summary).length == 0) {
            continue;
        }

        if (!row.ltv || Object.keys(row.ltv).length == 0) {
            continue;
        }
        summary = row.summary
        ltv = row.ltv
        break;
    }

    var money = util.moneyFormatter(consolidated_shop.currency)
    return [
        {
            key : 'aov',
            value : 'aov' in summary ? Math.round(summary.aov) : 0,
            value_display : 'aov' in summary ? money(Math.round(summary.aov)) : "0"
        },
        {
            key : 'customer_count',
            value : 'customer_count' in summary ? summary.customer_count : 0,
            value_display : 'customer_count' in summary ? summary.customer_count : "0"
        },
        {
            key : 'repeat_rate',
            value : 'repeat_rate' in summary ? Math.round(summary.repeat_rate) : 0,
            value_display : 'repeat_rate' in summary ? Math.round(summary.repeat_rate) : "0"
        },
        {
            key : 'ltv1m',
            value : 'ltv1m' in ltv ? Math.round(ltv.ltv1m) : 0,
            value_display : 'ltv1m' in ltv ? money(Math.round(ltv.ltv1m)) : "0"
        },
        {
            key : 'ltv3m',
            value : 'ltv3m' in ltv ? Math.round(ltv.ltv3m) : 0,
            value_display : 'ltv3m' in ltv ? money(Math.round(ltv.ltv3m)) : "0"
        },
        {
            key : 'ltv6m',
            value : 'ltv6m' in ltv ? Math.round(ltv.ltv6m) : 0,
            value_display : 'ltv6m' in ltv ? money(Math.round(ltv.ltv6m)) : "0"
        },
        {
            key : 'ltv1y',
            value : 'ltv1y' in ltv ? Math.round(ltv.ltv1y) : 0,
            value_display : 'ltv1y' in ltv ? money(Math.round(ltv.ltv1y)) : "0"
        },
        {
            key : 'ltv2y',
            value : 'ltv2y' in ltv ? Math.round(ltv.ltv2y) : 0,
            value_display : 'ltv2y' in ltv ? money(Math.round(ltv.ltv2y)) : "0"
        },
        {
            key : 'ltv3y',
            value : 'ltv3y' in ltv ? Math.round(ltv.ltv3y) : 0,
            value_display : 'ltv3y' in ltv ? money(Math.round(ltv.ltv3y)) : "0"
        }
    ];
}


function fillCohortsByData(cohorts, data, time_frame) {
    for (var k in data) {
        let row = data[k]
        if (row.period != time_frame) {
            continue;
        }
        var cohort_key = row.cohort ?? "";
        if (!cohort_key || !(cohort_key in cohorts)) {
            logger.warn('COHORT NOT FOUND', {key : cohort_key})
            continue;
        }

        let cohort = cohorts[cohort_key]
        cohort.repeat_customer_count = row.repeat_customer_count ?? 0;
        if (!!row.txn_freq && row.txn_freq.length > 0) {
            for (var t in row.txn_freq) {
                let txn_freq = row.txn_freq[t]
                cohort.txn_frequency_map[txn_freq.order_freq] = txn_freq.cust_count
            }
        }

        var order_bucket_key = row.bucket ?? "";

        let bucket = {}
        if (order_bucket_key == 'first_order') {
            // First Order Bucket
            bucket = cohort.first_order_bucket
        } else if (order_bucket_key in cohort.buckets) {
            // Same and Next TimeFrame buckets
            bucket = cohort.buckets[order_bucket_key]
        }

        if (Object.keys(bucket).length == 0) {
            logger.warn('BUCKET NOT FOUND', {key : order_bucket_key})
            continue;
        }

        bucket.order_count = row.order_count ?? 0;
        bucket.total_sales = row.total_price ?? 0;
        bucket.customer_count = row.customer_count ?? 0;
        if (order_bucket_key == 'first_order' || order_bucket_key == cohort_key) {
            cohort.first_month.order_count += row.order_count ?? 0;
            cohort.first_month.total_sales += row.total_price ?? 0;
            cohort.first_month.customer_count = Math.max(cohort.first_month.customer_count, (row.customer_count ?? 0))
        }
    }
}

function bucketToCellData(consolidated_shop, cohort, bucket, running_bucket, fo) {

    var perc_round = (x) => Math.round(x * 1000) / 10
    var div = (x, y) => {
        if (y > 0) {
            return x/y
        }
        return 0
    }

    var money = util.moneyFormatter(consolidated_shop.currency)

    var acc_order_count_per_customer = 0
    var acc_total_sales_per_customer = 0
    var cumulative_order_total = 0
    var cumulative_total_sales_total = 0
    var cumulative_aov_total = 0
    if (running_bucket.customer_count > 0) {
        acc_order_count_per_customer = running_bucket.order_count/running_bucket.customer_count
        acc_total_sales_per_customer = running_bucket.total_sales/running_bucket.customer_count
        cumulative_order_total = running_bucket.order_count
        cumulative_total_sales_total = running_bucket.total_sales
        cumulative_aov_total = div(running_bucket.total_sales, running_bucket.order_count)
    }

    // unique key for this data
    var dataRef = `${cohort.dataKey}:${bucket.dataKey}`
    var cellData = {
        aov : {
            value : Math.round(div(bucket.total_sales, bucket.order_count)),
            percentage : perc_round(div(div(bucket.total_sales, bucket.order_count), div(fo.total_sales, fo.order_count))),
            display_value : money(Math.round(div(bucket.total_sales, bucket.order_count))),
            display_percentage : `${perc_round(div(div(bucket.total_sales, bucket.order_count), div(fo.total_sales, fo.order_count)))}%`,
            display_cumulative_total : money(Math.round(cumulative_aov_total))
        },
        total_sales : {
            value : Math.round(bucket.total_sales),
            percentage : perc_round(div(bucket.total_sales, fo.total_sales)),
            display_value : money(Math.round(bucket.total_sales)),
            display_percentage : `${perc_round(div(bucket.total_sales, fo.total_sales))}%`,
            display_cumulative_total : money(Math.round(cumulative_total_sales_total))
        },
        order_count : {
            value : bucket.order_count,
            percentage : perc_round(div(bucket.order_count, fo.order_count)),
            display_value : bucket.order_count,
            display_percentage : `${perc_round(div(bucket.order_count, fo.order_count))}%`,
            display_cumulative_total : cumulative_order_total
        },
        customer_count : {
            value : bucket.customer_count,
            percentage : perc_round(div(bucket.customer_count, fo.customer_count)),
            display_value : bucket.customer_count,
            display_percentage : `${perc_round(div(bucket.customer_count, fo.customer_count))}%`,
            display_cumulative_total : 0
        },
        acc_order_count_per_customer : {
            value : Math.round(acc_order_count_per_customer * 100)/ 100,
            percentage : perc_round(div(acc_order_count_per_customer,div(fo.order_count, fo.customer_count))),
            display_value : Math.round(acc_order_count_per_customer * 100) / 100,
            display_percentage : `${perc_round(div(acc_order_count_per_customer,div(fo.order_count, fo.customer_count)))}%`,
            display_cumulative_total : cumulative_order_total
        },
        acc_total_sales_per_customer : {
            value : Math.round(acc_total_sales_per_customer),
            percentage : perc_round(div(acc_total_sales_per_customer,div(fo.total_sales, fo.customer_count))),
            display_value : money(Math.round(acc_total_sales_per_customer)),
            display_percentage : `${perc_round(div(acc_total_sales_per_customer,div(fo.total_sales, fo.customer_count)))}%`,
            display_cumulative_total : money(Math.round(cumulative_total_sales_total))
        },
        cohort_name : cohort.name,
        is_blur : cohort.blur ?? false,
        bucket_index : bucket.index,
        cohort_size : cohort.first_order_bucket.customer_count
    }
    return {dataRef, cellData}
}

async function calculateMetricsOnCohorts(cohorts, consolidated_shop) {
    var heatmap = {
        yLabels: [],
        xLabels : [],
        data : [],
        table : {
            new_customers : [],
            repeated_percentage : [],
            total_orders: [],
            total_revenue: [],
        },
        table_sum : {
            new_customers : 0,
            repeated_percentage : 0,
            total_orders : 0,
            total_revenue : 0,
        }
    }

    let weighted_avg = {};

    var area_chart = {
        cohorts : [],
        data : {} // changed to array later
    }

    var line_chart = {
        cohorts : [],
        data : {} // changed to array later
    }

    var money = util.moneyFormatter(consolidated_shop.currency)
    var data = {};

    for (var k in cohorts) {
        var cohort = cohorts[k];

        heatmap.yLabels.push(cohort.name)
        var isBlurred = cohort.blur ?? false;

        if (!isBlurred) {
            area_chart.cohorts.push({name: cohort.name, dataKey:cohort.dataKey})
            line_chart.cohorts.push({name: cohort.name, dataKey:cohort.dataKey})
        }

        // Running bucket for accumulated values
        var running_bucket = {
            total_sales : 0,
            order_count : 0,
            customer_count : cohort.first_order_bucket.customer_count
        }

        var heatmap_row = [];
        running_bucket.order_count += cohort.first_order_bucket.order_count
        running_bucket.total_sales += cohort.first_order_bucket.total_sales
        let {dataRef, cellData} = bucketToCellData(
            consolidated_shop,
            cohort,
            cohort.first_order_bucket,
            running_bucket,
            cohort.first_order_bucket
        )

        data[dataRef] = cellData
        heatmap_row.push(dataRef)

        if (!(cohort.first_order_bucket.name in weighted_avg)) {
            weighted_avg[cohort.first_order_bucket.name] = {
                value : 0,
                display_value : '',
                percentage : 0,
                display_percentage : '',
                customer_count : 0
            }
        }
        weighted_avg[cohort.first_order_bucket.name].value += running_bucket.total_sales
        weighted_avg[cohort.first_order_bucket.name].customer_count += running_bucket.customer_count

        for (var ck in cohort.buckets) {
            var bucket = cohort.buckets[ck]

            if (!isBlurred && !(ck in area_chart.data)) {
                area_chart.data[ck] = {bucket: bucket.chart_name}
            }

            if (!isBlurred && !(bucket.name in line_chart.data)) {
                line_chart.data[bucket.name] = {bucket : bucket.name}
            }

            running_bucket.order_count += bucket.order_count
            running_bucket.total_sales += bucket.total_sales
            let {dataRef, cellData} = bucketToCellData(
                consolidated_shop,
                cohort,
                bucket,
                running_bucket,
                cohort.first_order_bucket
            )
            data[dataRef] = cellData
            heatmap_row.push(dataRef)

            if (!(bucket.name in weighted_avg)) {
                weighted_avg[bucket.name] = {
                    value : 0,
                    display_value : '',
                    percentage : 0,
                    display_percentage : '',
                    customer_count : 0
                }
            }
            weighted_avg[bucket.name].value += running_bucket.total_sales
            weighted_avg[bucket.name].customer_count += running_bucket.customer_count

            if (cohort.dataKey == bucket.dataKey) {
                // For same month, add first_order_bucket for charts
                let {dataRef, cellData} = bucketToCellData(
                    consolidated_shop,
                    cohort,
                    cohort.first_month,
                    running_bucket,
                    cohort.first_order_bucket // TODO - think of this
                )
                if (!isBlurred) {
                    area_chart.data[ck][cohort.dataKey] = dataRef
                    line_chart.data[bucket.name][cohort.dataKey] = dataRef
                }
                data[dataRef] = cellData
            } else {
                if (!isBlurred) {
                    area_chart.data[ck][cohort.dataKey] = dataRef
                    line_chart.data[bucket.name][cohort.dataKey] = dataRef
                }
            }
        }

        var r_perc = 0
        if (cohort.first_order_bucket.customer_count > 0 && cohort.repeat_customer_count > 0) {
            r_perc = Math.round(100 * cohort.repeat_customer_count/ cohort.first_order_bucket.customer_count)
        }

        heatmap.table.total_revenue.push({
            is_blur : isBlurred,
            val : money(Math.round(running_bucket.total_sales))
        })
        heatmap.table.total_orders.push({
            is_blur : isBlurred,
            val : running_bucket.order_count
        })
        heatmap.table.repeated_percentage.push({
            is_blur : isBlurred,
            val : `${r_perc}%`
        })
        heatmap.table.new_customers.push({
            is_blur : isBlurred,
            val : cohort.first_order_bucket.customer_count
        })
        heatmap.data.push(heatmap_row)

        heatmap.table_sum.repeated_percentage  += cohort.repeat_customer_count
        heatmap.table_sum.total_revenue += running_bucket.total_sales
        heatmap.table_sum.total_orders += running_bucket.order_count
        heatmap.table_sum.new_customers += cohort.first_order_bucket.customer_count;
    }

    if (line_chart.cohorts.length) {
        line_chart.cohorts.unshift({name: "Weighted Avg", dataKey:'weighted_avg'})
        for (var bk in line_chart.data) {
            let dataRef = `weighted_avg:${bk}`;
            line_chart.data[bk]['weighted_avg'] = dataRef;
            data[dataRef] = {acc_total_sales_per_customer: weighted_avg[bk] ?? {}, bucket_index: bk, cohort_name: "Weighted Avg"}
        }
    }

    area_chart.data = Object.values(area_chart.data)
    line_chart.data = Object.values(line_chart.data)

    heatmap.table_sum.total_revenue = money(Math.round(heatmap.table_sum.total_revenue))
    if (heatmap.table_sum.new_customers > 0) {
        heatmap.table_sum.repeated_percentage = `${Math.round(100 * heatmap.table_sum.repeated_percentage/heatmap.table_sum.new_customers)}%`
    }

    var perc_round = (x) => Math.round(x * 1000) / 10
    for (var bucket_dk in weighted_avg) {
        weighted_avg[bucket_dk].value = Math.round(weighted_avg[bucket_dk].value/weighted_avg[bucket_dk].customer_count)
        weighted_avg[bucket_dk].display_value = money(weighted_avg[bucket_dk].value)
    }

    for (var bucket_dk in weighted_avg) {
        if (weighted_avg["First Order"] && weighted_avg["First Order"].value) {
            let val_perc = perc_round(weighted_avg[bucket_dk].value/weighted_avg["First Order"].value)
            weighted_avg[bucket_dk].percentage = val_perc
            weighted_avg[bucket_dk].display_percentage = `${val_perc}%`;
        }
    }

    // create xLabels
    if (Object.keys(cohorts).length > 0) {
        let {first_order_bucket, buckets} = Object.values(cohorts)[0]; // first cohort
        heatmap.xLabels = [first_order_bucket, ...Object.values(buckets)].map(b => b.name)
        heatmap.weighted = heatmap.xLabels.map(l => {
            return weighted_avg[l] || {}
        })
    }

    return {heatmap, area_chart, line_chart, data};
}

async function getCohortBreakdown(consolidated_shop, start_date, end_date, time_frame, applied_filters, frequency_group, breakdown, page_number, single_product_order, is_blur_active) {

    // Step 1 - Get data from BigQuery
    let single_product_order_filter = single_product_order == "enabled";
    let data = await bigquery.cohortBreakdown(
        consolidated_shop.shop_id,
        start_date,
        end_date,
        time_frame,
        applied_filters,
        breakdown,
        page_number,
        single_product_order_filter
    );

    // Step 2 - Create Cohorts
    let cohortList = {}
    let max_pages = 1
    for (var k in data) {
        if (!!data[k].max_pages) {
            max_pages = data[k].max_pages;
        }

        if (!data[k].cohort) {
            logger.warn('EMPTY COHORT', {breakdown, key : data[k].cohort})
            cohortList['<MISSING>'] = 1
            data[k].cohort = '<MISSING>';
            continue;
        }
        cohortList[data[k].cohort] = 1
    }

    var cohorts = await createBreakdownCohorts(
        Object.keys(cohortList),
        start_date,
        end_date,
        time_frame,
        is_blur_active
    )
    fillCohortsByData(cohorts, data, time_frame)

    // Step 3 - Calculate Metrics
    var [cohortAnalysis, cohortFrequency] = await Promise.all([
        calculateMetricsOnCohorts(cohorts, consolidated_shop),
        calculateTxnFrequencyOnCohorts(cohorts, frequency_group)
    ])

    // Step 4 - Create Response
    var response = {
        ...cohortAnalysis,
        cohort_frequency : cohortFrequency,
        currency : consolidated_shop.currency,
        trends : [],
        order_count : data.length,
        max_pages : max_pages
    }

    return response
}

async function getCohortAnalysis(consolidated_shop, start_date, end_date, time_frame, applied_filters, cohort_start_date, frequency_group) {

    // Step 1 - Get data from BigQuery
    let data = await bigquery.cohortAnalysis(
        consolidated_shop.shop_id,
        start_date,
        end_date,
        time_frame,
        applied_filters
    );

    // Step 2 - Create Cohorts
    var cohorts = await createTimeFrameCohorts(cohort_start_date, end_date, time_frame, start_date)
    fillCohortsByData(cohorts, data, time_frame)

    // Step 3 - Calculate Metrics
    var [cohortAnalysis, cohortFrequency, ltvTrends] = await Promise.all([
        calculateMetricsOnCohorts(cohorts, consolidated_shop),
        calculateTxnFrequencyOnCohorts(cohorts, frequency_group),
        getLTVTrends(start_date, end_date, data, consolidated_shop)
    ])

    // Step 4 - Create Response
    var response = {
        ...cohortAnalysis,
        cohort_frequency : cohortFrequency,
        currency : consolidated_shop.currency,
        trends : ltvTrends,
        order_count : data.length,
        max_pages : 1
    }

    return response
}

async function cohorts({consolidated_shop, start_date, end_date, time_frame, applied_filters, shop, frequency_group, breakdown, single_product_order, page_number, feature_access}) {

    const {is_valid, errors} = util.validate(consolidated_shop.shop_id, start_date, end_date, time_frame)
    logger.info('ltv.cohorts - ', {
        domain : consolidated_shop.myshopify_domain,
        start_date:start_date,
        end_date:end_date,
        time_frame:time_frame,
        breakdown: breakdown,
        single_product_order: single_product_order,
        page_number: page_number
    })

    if (!is_valid) {
        return {errors}
    }

    let cohort_start_date = dayjs(start_date).format('YYYY-MM-DD 00:00:00')
    let is_blur_active = false;
    let isCohortAnalysisQuery = !breakdown || breakdown == "acquisition_period";

    if (!!shop.shop_id && !!feature_access) {
        let isEnabled = await isFeatureEnabled(shop.shop_id, feature_access);
        if (!isEnabled) {
            cohort_start_date = dayjs(start_date).subtract(3, "month").format('YYYY-MM-DD 00:00:00')
            is_blur_active = true
        }
    }

    start_date = dayjs(start_date).format('YYYY-MM-DD 00:00:00')
    end_date = dayjs(end_date).format('YYYY-MM-DD 23:59:59')

    var response = {}
    try {
        if (isCohortAnalysisQuery) {
            response = await getCohortAnalysis(consolidated_shop, start_date, end_date, time_frame, applied_filters, cohort_start_date, frequency_group)
        } else {
            response = await getCohortBreakdown(consolidated_shop, start_date, end_date, time_frame, applied_filters, frequency_group, breakdown, page_number, single_product_order, is_blur_active)
        }
        response.is_blur_active = is_blur_active
    } catch (err) {
        logger.error('Error in cohorts', err)
        return {errors: [err.message]}
    }

    return response
}

export {cohorts}