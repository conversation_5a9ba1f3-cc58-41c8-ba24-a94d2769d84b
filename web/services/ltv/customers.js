import { createRequire } from "module";
const require = createRequire(import.meta.url);
const dayjs = require("dayjs");

import util from '../common/util.js';
import logger from '../logger.js';
import bigquery from '../bigquery/index.js';
import shop from '../shop.js';

async function profileJSON(consolidated_shop, start_date, end_date) {

    let useMigrationDataset = await shop.shouldUseMigrationDataset(consolidated_shop.shop_id);
    if (!useMigrationDataset) {
        return profileJSONLegacy(consolidated_shop, start_date, end_date)
    }

    logger.info('profileJSON - ', {shop_id: consolidated_shop.shop_id, start_date : start_date, end_date: end_date})

    const {is_valid, errors} = util.validate(consolidated_shop.shop_id, start_date, end_date)
    if (!is_valid) {
        return {error: errors.join(",")}
    }

    start_date = dayjs(start_date).format('YYYY-MM-DD 00:00:00')
    end_date = dayjs(end_date).format('YYYY-MM-DD 23:59:59')


    var customers = await bigquery.customerSegmentCustomers(consolidated_shop.shop_id, start_date, end_date);
    if (!customers || Object.keys(customers).length == 0) {
        return {error: "No data found"}
    }

    let fields = [
        "customer_id",
        "email",
        "first_name",
        "last_name",
        "phone",
        "city",
        "province",
        "country",
        "zip",
        "order_count",
        "order_total",
        "quartile",
        "quartile_name",
        "first_order_date"
    ];

    return {data : customers, fields};
}

// uses legacy bigquery queries and limited exported fields
async function profileJSONLegacy(consolidated_shop, start_date, end_date) {

    logger.info('profileJSON - ', {shop_id: consolidated_shop.shop_id, start_date : start_date, end_date: end_date})

    const {is_valid, errors} = util.validate(consolidated_shop.shop_id, start_date, end_date)
    if (!is_valid) {
        return {error: errors.join(",")}
    }

    start_date = dayjs(start_date).format('YYYY-MM-DD 00:00:00')
    end_date = dayjs(end_date).format('YYYY-MM-DD 23:59:59')


    var customers = await bigquery.customerSegmentCustomersLegacy(consolidated_shop.shop_id, start_date, end_date);
    if (!customers || Object.keys(customers).length == 0) {
        return {error: "No data found"}
    }

    let fields = [
        "customer_id",
        "email",
        "first_name",
        "last_name",
        "phone",
        "order_count",
        "order_total",
        "quartile",
        "quartile_name",
        "first_order_date"
    ];

    return {data : customers, fields};
}

async function getCustomerQuartiles(consolidated_shop, quartiles) {

    var money = util.moneyFormatter(consolidated_shop.currency)

    if (!quartiles || quartiles.length == 0) {
        return {
            data : []
        }
    }
    
    let quartile = (name) => {
        return {
            quartile : name,
            order : 0,
            revenue : '',
            order_freq : 0,
            ltv : 0,
            ltv_percent : 0
        }
    }

    var percentile_table_data = {
        "q1" : quartile("1st (Top 25%)"),
        "q2" : quartile("2nd (25% - 50%)"),
        "q3" : quartile("3rd (50% - 75%)"),
        "q4" : quartile("4th (Last 25%)"),
    };

    let max_ltv = 0;
    for (var ck in quartiles) {
        let q = quartiles[ck]
        let bucket_key = `q${q.quartile}`
        if (q.order_count > 0 && !!percentile_table_data[bucket_key]) {
            percentile_table_data[bucket_key].order_freq = q.order_frequency;
            percentile_table_data[bucket_key].ltv = money(Math.round(q.ltv));
            percentile_table_data[bucket_key].ltv_value = Math.round(q.ltv);
            max_ltv = Math.max(max_ltv, percentile_table_data[bucket_key].ltv_value)
            percentile_table_data[bucket_key].revenue = money(Math.round(q.total_revenue));
            percentile_table_data[bucket_key].order = q.order_count
        }
    }

    for (var bkt in percentile_table_data) {
        if (max_ltv > 0) {
            percentile_table_data[bkt].ltv_percent = (percentile_table_data[bkt].ltv_value/max_ltv) * 100
        }
    }

    return {
        data: Object.values(percentile_table_data)
    }
}

async function getCustomerTransactionFrequency(transaction_frequency) {

    if (!transaction_frequency || transaction_frequency.length == 0) {
        return []
    }

    var txn_frequency_map = {
        '1txn': { name: "cust-order-1-title", value: 0, display : "cust-order-1-desc"},
        '2txn': { name: "cust-order-2-title", value: 0, display : "cust-order-2-desc"},
        '3txn': { name: "cust-order-3-title", value: 0, display : "cust-order-3-desc" },
        '4txn': { name: "cust-order-4-title", value: 0, display : "cust-order-4-desc" },
        'default' : { name: "cust-order-4+-title", value: 0, display : "cust-order-4+-desc"}
    }

    for (var ck in transaction_frequency) {
        let freq = transaction_frequency[ck]
        if (`${freq.order_freq}txn` in txn_frequency_map) {
            txn_frequency_map[`${freq.order_freq}txn`].value = freq.cust_count ?? 0;
        } else {
            txn_frequency_map[`default`].value += (freq.cust_count ?? 0);
        }
    }

    return Object.values(txn_frequency_map)
}

async function profile({consolidated_shop, start_date, end_date}) {

    const {is_valid, errors} = util.validate(consolidated_shop.shop_id, start_date, end_date)
    if (!is_valid) {
        return {errors}
    }

    start_date = dayjs(start_date).format('YYYY-MM-DD 00:00:00')
    end_date = dayjs(end_date).format('YYYY-MM-DD 23:59:59')

    let result = await bigquery.customerSegments(consolidated_shop.shop_id, start_date, end_date);
    if (!result || Object.keys(result).length == 0) {
        return {errors: ["No data found"]}
    }

    var money = util.moneyFormatter(consolidated_shop.currency)

    if (!result.percentiles || result.percentiles.length == 0) {
        result.percentiles = [];
    }
    var percentile_chart = []
    for (var ind = 0; ind < result.percentiles.length && ind < 100; ind++) {
        let val = result.percentiles[ind]
        if (!val) {
            val = 0
        }
        percentile_chart.push({
            name : `${ind}%`,
            top_percentage : `${100 - ind}%`,
            value : val,
            value_formatted : money(val)
        })
    }


    return {
        order_count : await getCustomerTransactionFrequency(result.transaction_frequency),
        percentile_table : await getCustomerQuartiles(consolidated_shop, result.quartiles),
        percentile_chart
    }

}

export {profile, profileJSON}