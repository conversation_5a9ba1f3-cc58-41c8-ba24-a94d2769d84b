import util from '../common/util.js';
import bigquery from '../bigquery/index.js';

async function repurchaseRateBreakdown({consolidated_shop, start_date, end_date, group_value, repurchase_window}) {

    const {is_valid, errors} = util.validate(consolidated_shop.shop_id, start_date, end_date)
    if (!is_valid) {
        return {errors}
    }

    var result = {product_ltv : [], product_type_ltv:[], product_types : []}
    repurchase_window = parseInt(repurchase_window || '100000');
    if (repurchase_window < 0 || repurchase_window == NaN) {
        repurchase_window = 100000
    }
    if (group_value == 'product') {
        result.product_ltv = await bigquery.productRepurchases(consolidated_shop.shop_id, start_date, end_date, repurchase_window)
    } else if (group_value == 'product_type') {
        result.product_type_ltv = await bigquery.productTypeRepurchases(consolidated_shop.shop_id, start_date, end_date, repurchase_window)
    }
    let product_types = {}

    var money = util.moneyFormatter(consolidated_shop.currency)

    for (var k in result.product_ltv) {
        if (('product_type' in result.product_ltv[k]) && result.product_ltv[k].product_type != '') {
            product_types[result.product_ltv[k].product_type] = 1;
        }
        result.product_ltv[k].aov_display = money(result.product_ltv[k].aov)
        result.product_ltv[k].ltv_display = money(result.product_ltv[k].ltv)
        result.product_ltv[k].ltv_90d_display = money(result.product_ltv[k].ltv_90d)
        result.product_ltv[k].ltv_180d_display = money(result.product_ltv[k].ltv_180d)
    }

    for (var k in result.product_type_ltv) {
        if (('product_type' in result.product_type_ltv[k]) && result.product_type_ltv[k].product_type != '') {
            product_types[result.product_type_ltv[k].product_type] = 1;
        }
        result.product_type_ltv[k].aov_display = money(result.product_type_ltv[k].aov)
        result.product_type_ltv[k].ltv_display = money(result.product_type_ltv[k].ltv)
        result.product_type_ltv[k].ltv_90d_display = money(result.product_type_ltv[k].ltv_90d)
        result.product_type_ltv[k].ltv_180d_display = money(result.product_type_ltv[k].ltv_180d)
    }

    result.product_types = Object.keys(product_types);

    return result
}

export {repurchaseRateBreakdown}