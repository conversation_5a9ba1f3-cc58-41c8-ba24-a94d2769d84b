import { SOURCE_GOOGLE_ADS, SOURCE_FB, SOURCE_GOOGLE_ANALYTICS } from '../../airbyte/constants.js';
import logger from '../../logger.js';
import integrations from '../../airbyte/integrations.js';
import errors from '../../errors.js';
import google_ads from '../google_ads/index.js';
import facebook from '../facebook/index.js';
import google_analytics from '../google_analytics/index.js';
import shopify from '../shopify/index.js';
import blended from '../blended/index.js';

const combinedSummary = async (shop_id, group_shop_ids, filters) => {
    let response = {
        data: {
            currency: "",
            current: [],
            previous: [],
            currentAgg : {},
            previousAgg: {}
        }
    }

    const {integration_request_ids, metrics} = filters;

    let integrations_to_filter = [];
    if (!!integration_request_ids && integration_request_ids.length > 0) {
        integrations_to_filter = integration_request_ids;
    }

    let intgs = await integrations.getIntegrations(shop_id, "", false, false)

    let fbIntgs = (intgs[SOURCE_FB] ?? []).filter(intg => integrations_to_filter.includes(intg.request_id))
    let facebookAdsSummary = await facebook.summary(fbIntgs, filters)

    let googleAdsIntgs = (intgs[SOURCE_GOOGLE_ADS] ?? []).filter(intg => integrations_to_filter.includes(intg.request_id))
    let googleAdsSummary = await google_ads.summary(googleAdsIntgs, filters)

    let gaIntgs = (intgs[SOURCE_GOOGLE_ANALYTICS] ?? []).filter(intg => integrations_to_filter.includes(intg.request_id))
    let gaSummary = await google_analytics.summary(gaIntgs, filters)


    let shopifySummary = {}
    let add_shop_metrics = false;

    // parse metrics to check if shopify metrics are required
    if (metrics) {
        metrics.forEach(metric => {
            if (metric.startsWith("shopify:")) {
                add_shop_metrics = true
            }
        })
    }

    if (add_shop_metrics) {
        shopifySummary = await shopify.summary(group_shop_ids, filters)
    }

    // Blended metrics for Google ads and Facebook ads
    if (googleAdsSummary.error || facebookAdsSummary.error || gaSummary.error) {
        logger.error('metrics.summary error', {errors: [googleAdsSummary.error, facebookAdsSummary.error, gaSummary.error]})
        response.error = errors.client.generic;
        return response;
    }

    const mergeEntriesByBucketId = (entriesArr) => {
        const mergedEntries = {};
        entriesArr.forEach(entries => {
            entries.forEach(entry => {
                if (!mergedEntries[entry.bucketId]) {
                    mergedEntries[entry.bucketId] = {...entry}
                } else {
                    mergedEntries[entry.bucketId] = {
                        ...mergedEntries[entry.bucketId],
                        ...entry
                    }
                }
            });
        });
        return Object.values(mergedEntries);
    }

    // TODO - currency conversion
    response.data.currency = googleAdsSummary.data?.currency
        || facebookAdsSummary.data?.currency
        || gaSummary.data?.currency
        || shopifySummary.data?.currency
        || "";

    // Merging current and previous entries
    response.data.current = mergeEntriesByBucketId([
        googleAdsSummary.data?.current ?? [],
        facebookAdsSummary.data?.current ?? [],
        gaSummary.data?.current ?? [],
        shopifySummary.data?.current ?? []
    ]);

    response.data.previous = mergeEntriesByBucketId([
        googleAdsSummary.data?.previous ?? [],
        facebookAdsSummary.data?.previous ?? [],
        gaSummary.data?.previous ?? [],
        shopifySummary.data?.previous ?? []
    ]);

    // Merging current and previous aggregated entries
    response.data.currentAgg = {
        ...(googleAdsSummary.data?.currentAgg ?? {}),
        ...(facebookAdsSummary.data?.currentAgg ?? {}),
        ...(gaSummary.data?.currentAgg ?? {}),
        ...(shopifySummary.data?.currentAgg ?? {})
    }

    response.data.previousAgg = {
        ...(googleAdsSummary.data?.previousAgg ?? {}),
        ...(facebookAdsSummary.data?.previousAgg ?? {}),
        ...(gaSummary.data?.previousAgg ?? {}),
        ...(shopifySummary.data?.previousAgg ?? {})
    }

    return response;
}

const summary = async (shop_id, group_shop_ids, filters) => {
    let response = await combinedSummary(shop_id, group_shop_ids, filters);

    // Apply formulae to each entry in the current and previous arrays
    response.data.current = response.data.current.map(data => blended.addCalculatedMetrics(blended.blendedMetrics, data));
    response.data.previous = response.data.previous.map(data => blended.addCalculatedMetrics(blended.blendedMetrics, data));

    // Apply formulae to currentAgg and previousAgg aggregates
    response.data.currentAgg = blended.addCalculatedMetrics(blended.blendedMetrics, response.data.currentAgg);
    response.data.previousAgg = blended.addCalculatedMetrics(blended.blendedMetrics, response.data.previousAgg);

    return response;
};

export default {
    summary
}
  