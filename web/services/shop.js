import { createRequire } from "module";
const require = createRequire(import.meta.url);
const {CloudTasksClient} = require('@google-cloud/tasks');
const Shopify = require('shopify-api-node');
var Intercom = require('intercom-client');
const axios = require('axios');
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import relativeTime from "dayjs/plugin/relativeTime";
import isoWeek from "dayjs/plugin/isoWeek";
import automation from "./automation/index.js";
import crypto from "crypto";
import unleash from "./unleash.js";

dayjs.extend(isoWeek);
dayjs.extend(relativeTime);
dayjs.extend(utc)
dayjs.extend(timezone)

const DATA_TIME_ZONE = 'Asia/Kolkata';
const WORKSPACE_TYPE_OWNER = "owner";
const SHOPIFY_API_VERSION = "2024-10";

import mysql from "./database.js";
import tracker from "./tracker.js";
import TokenService from './token.js';
import logger from './logger.js';
import {getActiveSubscription, getAggregatedActiveSubscription} from './subscription.js'
import { cache, redis } from './redis.js';
import {cubejsInstance} from "./cube/instance.js";
import integrations from "./airbyte/integrations.js";
import secretmanager from "./secretmanager/index.js";
import bqRuns from "./bigquery/run.js";
import { getActivePlanDetails, getPlanDetails} from "./subscription/plans.js";

const SHOP_STATE_PREFIX = 'shop_state_';

async function updateShopState(shop_id, key, value) {
    let state = await cache.get(SHOP_STATE_PREFIX + shop_id)
    if (state == null || Object.keys(state).length == 0) {
        state = {}    
    }
    state[key] = value
    return cache.set(SHOP_STATE_PREFIX + shop_id, state)
}


async function getShopState(shop_id) {
    let state = await cache.get(SHOP_STATE_PREFIX + shop_id)
    if (state == null || Object.keys(state).length == 0) {
        state = {}    
    }
    return state
}


async function getShopifyClient(shopName, accessToken) {
    const shopify = new Shopify({
        shopName: shopName,
        accessToken : accessToken,
        autoLimit : true,
        apiVersion: SHOPIFY_API_VERSION,
        hooks: {
            afterResponse: [
              (response) => {
                let request = {};
                if (response.request) {
                    const requestUrl = response.request.requestUrl || 'Unknown URL';
                    const method = response.request.options?.method || 'Unknown Method';
                    const body = response.request.options?.json || 'No Request Body';
                    request = {requestUrl, method, body};
                }

                if ('x-shopify-api-version' in response.headers) {
                    logger.info('[shopify-api-node-x] API version:' + response.headers['x-shopify-api-version']);
                    // Log the API version if it doesn't match the expected version
                    if (response.headers['x-shopify-api-version'] != SHOPIFY_API_VERSION) {
                        logger.warn('[shopify-api-node-x] API version mismatch:' + response.headers['x-shopify-api-version'], {request});
                    }
                }

                // Log if a deprecated API call was made
                if ('x-shopify-api-deprecated-reason' in response.headers) {
                    logger.error(
                        '[shopify-api-node-x] Deprecated API Call:' + response.headers['x-shopify-api-deprecated-reason'],
                        {request}
                    );
                }

                logger.info('[shopify-api-node-x] Response Headers:', response.headers);
                return response;
              },
            ],
        },
    });

    shopify.on('callLimits', (limits) => {
        if (limits.remaining < 5) {
            logger.error('Reaching shopify API limit')
        }
    });

    return shopify
}

function ISTDateFormatter(value) {

    if (value === null) {
        // null date times from api should stay that way
        return value
    }

    if (!!value) {
        return dayjs.utc(value).utcOffset(330).format('YYYY-MM-DD HH:mm:ss')
    }

    return dayjs.utc().utcOffset(330).format('YYYY-MM-DD HH:mm:ss')
}

// Migration for shopify from legacy to airbyte oss
// Contraints to switch to new dbt_prod (EU) dataset for shopify instead of processed (US)
// shop.is_migration_ready = true
// switched on after airbyte_oss sync is finished and dbt transformation is done
async function shouldUseMigrationDataset(shop_id) {

    if (Array.isArray(shop_id) && shop_id.length > 0) {
        // TODO - cases for consolidated shops is an issue - we default back to legacy in this case
        return false;
    }

    let shopObj = await getShopById(shop_id);
    if (!shopObj || !shopObj.shop_id) {
        return false;
    }

    if (!('is_migration_ready' in shopObj) || !shopObj.is_migration_ready) {
        return false
    }

    let featureContext = {
        myshopify_domain: shopObj.myshopify_domain,
        installed_at: dayjs(shopObj.row_created_at).format("YYYYMMDD"),
        country_name: shopObj.country_name,
        plan_display_name: shopObj.plan_display_name
    };

    const shopify_oss_migration = unleash.isEnabled('shopify_oss_migration', featureContext);
    return shopify_oss_migration
}

// updatePartial function receives a subset of database columns to update
export const updatePartial = async function (shop_id, partial_update) {

    let shopObj = await getShopById(shop_id)
    if (!shopObj || !shopObj.shop_id) {
        // request id is invalid
        return {}
    }

    delete shopObj.row_updated_at
    delete shopObj.row_created_at

    let newShopObj = Object.assign(shopObj, partial_update)

    try {
        await mysql.query("UPDATE shops SET ? WHERE shop_id = ?", [newShopObj, shop_id]);
        await clearShopCache(shopObj.myshopify_domain, shopObj.shop_id)
        return newShopObj;
    } catch (err) {
        logger.error('shop.updatePartial: ', err);
        return {};
    }
}

function extractShopData(data) {
    return {
        shop_id: data.id,
        name: data.name,
        domain: data.domain,
        myshopify_domain: data.myshopify_domain,
        shop_owner: data.shop_owner ?? '',
        customer_email: data.customer_email ?? '',
        phone: data.phone,
        currency: data.currency ?? '',
        city: data.city ?? '',
        country: data.country ?? '',
        active_flag : 1,
        timezone: data.timezone ?? '',
        created_at : ISTDateFormatter(data.created_at),
        country_name: data.country_name ?? '',
        province_code: data.province_code ?? '',
        province: data.province ?? '',
        plan_display_name: data.plan_display_name ?? '',
        plan_name: data.plan_name ?? '',
        iana_timezone: data.iana_timezone ?? '',
    }
}

async function save(data) {
    try {
        var connection = await mysql.connection();
    } catch (err) {
        logger.error('shop.save : mysql connection failure', err);
        throw err;
    }

    var shopObj = extractShopData(data)
    try {
        let shopData = await connection.query('SELECT * FROM shops WHERE shop_id = ?', [shopObj.shop_id + '']);
        if (shopData.length == 0) {
            await connection.query("INSERT INTO shops SET ?", shopObj);
        } else {
            await connection.query("UPDATE shops SET ? WHERE shop_id = ?", [shopObj, shopObj.shop_id + '']);
        }
        return shopObj;
    } catch (err) {
        logger.error('shop.save: ', err);
        throw err;
    } finally {
        await connection.release();
    }
}

export const slackAlert = async (msg, channel = '#alerts') => {
    try {
        const url = 'https://slack.com/api/chat.postMessage';
        const res = await axios.post(url, {
          channel,
          text: msg
        }, { headers: { authorization: `Bearer ${process.env.SLACK_TOKEN}` } });
        logger.info('Done', res.data);
    } catch (err) {
        logger.error('slackAlert : error - ', err);
    }
}

// responsible for fetching and saving shop data
async function syncShopData(shop, accessToken) {
    var shopData = {}
    try {
        var shopify = await getShopifyClient(shop, accessToken)
        var data = await shopify.shop.get()
        shopData = await save(data)
    } catch (err) {
        logger.error('shop.sync: ', err);
        throw err;
    }

    await slackAlert("New shop installed - " + (shopData.myshopify_domain ?? "") + "", '#customer-engagement')
    if (!!shopData.myshopify_domain) {
        tracker.track(shopData.myshopify_domain , 'Shop Installed')
    }
    return shopData
} 


async function initiateDataSync(shop, accessToken, fullSyncRequired, isShopifyPlus = false) {

    if (process.env.NODE_ENV !== "production") {
        return false
    }

    const project = process.env.PROJECT_ID;
    const queue = process.env.DATA_SYNC_QUEUE;
    const location = process.env.DATA_SYNC_QUEUE_LOCATION;

    const payload = {
        shop : shop,
        accessToken : accessToken,
        isNewShop : 1,
        fullSyncRequired: fullSyncRequired ? 1 : 0,
        isShopifyPlus: isShopifyPlus ? 1 : 0
    };

    const client = new CloudTasksClient();
    async function createTask() {
        const parent = client.queuePath(project, location, queue);

        const task = {
            appEngineHttpRequest: {
                httpMethod: 'POST',
                relativeUri: '/start_sync',
                body : Buffer.from(JSON.stringify(payload)).toString('base64')
            },
        };

        logger.info('Sending task:', task);
        const request = {parent, task};
        const [response] = await client.createTask(request);
        const name = response.name;
        logger.info(`Created task ${name}`);
    }

    await createTask()
}


async function processAsyncTask(payload = {}) {

    logger.info('task received', payload);
    if (!payload.action) {
        logger.error("No action specified")
        return false;
    }

    const actionMap = {
        "triggerCustomerEngagementChat" : triggerCustomerEngagementChat,
        "shoffiAffliatePOSTCall" : shoffiAffliate,
        "automation" : automation.run,
        "saveTokenMetadata" : secretmanager.saveTokenMetadata,
        "setupDataPipeline" : integrations.setupDataPipeline,
        "runDatadrewEUSync" : bqRuns.runDatadrewEUSync
    }

    if (!(payload.action in actionMap)) {
        logger.error("Invalid action specified")
        return false;
    }

    try {
        let action = actionMap[payload.action]
        let done = await action(payload)
        return done;
    } catch (err) {
        logger.error('processAsyncTask error : ', {message : err.message, payload})
        return false;
    }
}

async function pushAsyncTask(payload = {}) {

    if (process.env.NODE_ENV !== "production") {
        let done = await processAsyncTask(payload)
        return done;
    }

    const project = process.env.PROJECT_ID;
    const queue = "async-push";
    const location = "us-central1";
    const authToken = process.env.CUSTOM_AUTH_TOKEN

    const client = new CloudTasksClient();
    async function createTask() {
        const parent = client.queuePath(project, location, queue);

        const task = {
            httpRequest: {
                headers: {
                  'Content-Type': 'application/json',
                  'CustomAuthToken' : authToken
                },
                httpMethod: 'POST',
                url : process.env.WORKER_HOST + "/api/async-push",
                body : Buffer.from(JSON.stringify(payload)).toString('base64')
            },
        };

        logger.info('Sending task:', task);
        const request = {parent, task};
        const [response] = await client.createTask(request);
        const name = response.name;
        logger.info(`Created task ${name}`);
    }

    await createTask()
}

async function fetchOrderCount(shop_id) {
    var count = 0
    try {
        let order_count = await mysql.query('SELECT count(*) as c FROM orders WHERE shop_id = ?', [shop_id + '']);
        count = order_count[0].c
    } catch (err) {
        logger.error('shop.fetchOrderCount error : ', err)
    }

    return count
}

async function clearShopCache(shopOrigin, shopId) {
    let c1 = await cache.del(getShopByOriginCacheKey(shopOrigin))
    let c2 = await cache.del(getShopByIdCacheKey(shopId))
    return c1 && c2;
}

function getShopByOriginCacheKey(shopOrigin) {
    // cache key here is kept consistent with worker shop.js file
    return `shop_by_origin_v6_${shopOrigin}`
}

function getShopByIdCacheKey(shopId) {
    // cache key here is kept consistent with worker shop.js file
    return `shop_v4_${shopId}`
}

async function getShopByOrigin(shopOrigin) {

    if (!shopOrigin) {
        return {}
    }

    var cacheKey = getShopByOriginCacheKey(shopOrigin);
    var fromCache = await cache.get(cacheKey);
    if (fromCache !== null) {
        return fromCache
    }

    var shop = {}
    try {
        let result = await mysql.query('SELECT * FROM shops WHERE myshopify_domain = ?', [shopOrigin]);
        shop = result[0]
    } catch (err) {
        logger.error('shop.getShopByOrigin error : ', err)
    }

    if (shop) {
        cache.set(cacheKey, shop)
    }
    return shop
}

async function getShopById(shop_id) {
    var cacheKey = getShopByIdCacheKey(shop_id);
    var fromCache = await cache.get(cacheKey);
    if (fromCache !== null) {
        return fromCache
    }

    var shop = {}
    try {
        let result = await mysql.query('SELECT * FROM shops WHERE shop_id = ?', [shop_id + '']);
        shop = result[0]
    } catch (err) {
        logger.error('shop.getShopById error : ', err)
    }

    if (shop) {
        cache.set(cacheKey, shop)
    }
    return shop
}

async function triggerCustomerEngagementChat({origin}) {

    if (origin == process.env.TEST_STORE) {
        return true
    }
    
    try {
        const client = new Intercom.Client({ tokenAuth: { token: process.env.INTERCOM_ACCESS_TOKEN } });

        let shopObj = await getShopByOrigin(origin)
        if (!shopObj || !shopObj.shop_id) {
            return false
        }

        let subsObj = await getActiveSubscription(shopObj.shop_id)

        client.useRequestOpts({
            headers: {
                'Intercom-Version': 2.6
            }
        });

        let ll = await client.contacts.search({
            data : {
                query: {
                    field: "external_id",
                    operator: "=",
                    value: origin
                }
            }
        })


        let subscribed = "No";
        if (subsObj && subsObj.subscription_id) {
            subscribed = `Yes, ${dayjs(subsObj.row_created_at).format("DD MMM, YYYY")}`;
        }

        if (!ll.data.length) {
            logger.error('triggerCustomerEngagementChat no data - ', origin)
            return false
        }

        let intercom_id = ll.data[0].id
        let chatLink = `https://app.intercom.com/a/inbox/gjo3rv2i/inbox/new-conversation?recipient=${intercom_id}`
        let pastReviews = `https://appnavigator.io/store/${origin.replace(".myshopify.com", "")}`

        let msg = `${origin} is Online. \nSubscribed: ${subscribed}\nPlan : ${shopObj.plan_display_name}\nInstalled : ${dayjs(shopObj.row_created_at).format("MMM, YYYY")}\nUser: ${shopObj.onboard_name}(${shopObj.onboard_email})\nAppnavigator: ${pastReviews}\nChat: ${chatLink}\n`
        await slackAlert(msg, '#customer-engagement');
        return true
    } catch (err) {
        logger.error('triggerCustomerEngagementChat error - ', err)
        return false
    }
}

async function updateIntercomUser(origin, customAttributes) {
    try {
        const client = new Intercom.Client({ tokenAuth: { token: process.env.INTERCOM_ACCESS_TOKEN } });

        client.useRequestOpts({
            headers: {
                'Intercom-Version': 2.6
            }
        });

        let ll = await client.contacts.search({
            data : {
                query: {
                    field: "external_id",
                    operator: "=",
                    value: origin
                }
            }
        })


        if (!ll.data.length) {
            logger.warn('updateIntercomUser user not found - ', {origin})
            return
        }

        let intercom_id = ll.data[0].id

        await client.contacts.update({
            role : Intercom.Role.USER,
            id	: intercom_id,
            customAttributes: customAttributes
        })
    } catch (err) {
        logger.error('updateIntercomUser error - ', err)
    }
}

async function afterUninstall(origin, shop_id) {
    try {
        await mysql.query("UPDATE shops SET active_flag = 0, uninstalled_at = ? WHERE shop_id = ?", [ISTDateFormatter(), shop_id + '']);
        await clearShopCache(origin, shop_id)
        return true;
    } catch (err) {
        logger.error('shop.afterUninstall: ', err);
        throw err;
    }
}

async function setShopAsActive(origin, shop_id) {
    try {
        await mysql.query("UPDATE shops SET active_flag = 1, last_sync_at = NULL WHERE shop_id = ?", [shop_id + '']);
        await clearShopCache(origin, shop_id)
        return true;
    } catch (err) {
        logger.error('shop.setShopAsActive: ', err);
        throw err;
    }
}

async function afterAuth(req, res, next) {

    var {shop, accessToken, scope} = res.locals.shopify.session

    var shopObj = await getShopByOrigin(shop)
    var firstInstall = false
    // shop not found - should be first install
    if (!shopObj || !shopObj.shop_id) {
        firstInstall = true
        shopObj = await syncShopData(shop, accessToken)
    }

    // Not on shoffi right now
    if (false && firstInstall) {
        let xff = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
        await pushAsyncTask({
            action : "shoffiAffliatePOSTCall",
            shopName : shop,
            xff : xff
        })
    }

    if (!shopObj.active_flag) {
        // reinstalls are treated as first install
        firstInstall = true
        await setShopAsActive(shop, shopObj.shop_id)
    }

    // run cloud task - sync data on every login
    await initiateDataSync(shop, accessToken, firstInstall, shopObj.plan_name == "shopify_plus")

    if (!firstInstall) {
        await pushAsyncTask({ origin : shop, action : "triggerCustomerEngagementChat"})
    }

    // create or update access token
    var Token = new TokenService(shopObj.shop_id)
    const done = await Token.saveToken(shopObj.myshopify_domain, accessToken, scope)
    logger.info('saveToken' , done)
    return await next()
}

async function getAllShops() {
    var shops = []
    try {
        let result = await mysql.query('SELECT * FROM shops order by row_created_at desc');
        shops = result
    } catch (err) {
        logger.error('shop.getAllShops error : ', err)
    }
    return shops
}

async function saveOnboardingDetails({shop, onboard_email, onboard_name, onboard_industry, onboard_user_type, onboard_phone}) {

    onboard_email = onboard_email ?? '';
    onboard_phone = onboard_phone ?? '';
    onboard_name = onboard_name ?? '';
    onboard_industry = onboard_industry ?? '';
    onboard_user_type = onboard_user_type ?? '';


    let params = [
        onboard_email,
        onboard_name,
        onboard_phone,
        onboard_industry,
        onboard_user_type,
        onboard_email,
        shop.shop_id + ''
    ];

    try {
        await mysql.query(`
            UPDATE shops
            SET onboard_email = ?,
                onboard_name = ?,
                onboard_phone = ?,
                onboard_industry = ?,
                onboard_user_type = ?,
                report_emails = ?
            WHERE shop_id = ?`,
            params
        );
        await clearShopCache(shop.myshopify_domain, shop.shop_id)
        return {status : true};
    } catch (err) {
        logger.error('shop.saveOnboardingDetails: ', err);
        return {status : false}
    }
}

// returns valid shopOptions - for UI - by passing original authenticated user / shop
async function getShopOptions(shop, user_data, user_shops, is_admin) {

    let options = {
        shopOptions : [],
        adminShopOptions : []
    }

    if (user_data && user_data.uid) {
        // adding shops for user flow - only shops mapped to user
        options.shopOptions = user_shops.map((s) => {
            return {
                shop_id : s.shop_id,
                name : s.name,
                myshopify_domain : s.myshopify_domain,
                domain : s.domain,
                is_store : true,
                active_flag : s.active_flag
            };
        });

        // add new store as an option in user flow 
        options.shopOptions.push({
            shop_id : '',
            nameKey : "add-new-store",
            myshopify_domain : 'add-new-store',
            domain : '',
            is_store : false,
            active_flag : 1
        });
    } else if (shop && shop.myshopify_domain) {
        // adding shop for single shopify session
        options.shopOptions = [{
            shop_id : shop.shop_id,
            name : shop.name,
            myshopify_domain : shop.myshopify_domain,
            domain : shop.domain,
            is_store : true,
            active_flag : shop.active_flag
        }]

        // add new store as an option in shopify flow 
        options.shopOptions.push({
            shop_id : '',
            nameKey : "multi-store-sign-up",
            myshopify_domain : 'multi-store-sign-up',
            domain : '',
            active_flag : 1,
            is_store : false
        });
    }

    if (options.shopOptions.length > 2) {
        options.shopOptions = [{
            shop_id : '',
            nameKey : 'consolidated',
            myshopify_domain : 'consolidated.myshopify.com',
            domain : '',
            active_flag : 1,
            is_store : false
        }, ...options.shopOptions]
    }

    // isAdmin - add all shops
    if (is_admin) {
        let allShops = await getAllShops()
        options.adminShopOptions = allShops.map((s) => {
            return {
                shop_id : s.shop_id,
                name : s.name,
                myshopify_domain : s.myshopify_domain,
                domain : s.domain,
                active_flag : s.active_flag,
                is_store : true
            };
        })
    }

    return options
}

const getIntercomUserHash = (user_id) => {

    if (!user_id || !process.env.INTERCOM_SECRET_KEY) {
        return ''
    }

    try {
        const user_hash = crypto
            .createHmac('sha256', process.env.INTERCOM_SECRET_KEY)
            .update(user_id)
            .digest('hex');
        return user_hash
    } catch (err) {
        logger.error('getIntercomUserHash error : ', err)
        return ''
    }
}

async function getLoginTrackingConfig(shop_data, user_data) {
    let isUserFlow = !!user_data && Object.keys(user_data).length > 0

    if (isUserFlow) {
        let trackable_common_user = {
            'user_type': 'user',
            'firebase_uid' : user_data.uid,
            'active_flag': 1,
            'workspace_name' : user_data.workspace_name ?? '',
            'workspace_type' : user_data.workspace_type ?? '',
            'onboard_email': user_data.onboard_email ?? '',
            'onboard_name': user_data.onboard_name ?? '',
            'onboard_industry': user_data.onboard_industry ?? '',
            'onboard_user_type': user_data.onboard_user_type ?? '',
            'onboard_phone': user_data.onboard_phone ?? '',
        }

        let trackable_intercom = {
            'email' : user_data.email,
            'created_at': user_data.row_created_at,
            'user_id' : user_data.email,
            ...trackable_common_user
        }

        let trackable_mixpanel = {
            '$email' : user_data.email,
            '$created': user_data.row_created_at,
            ...trackable_common_user
        }

        // Intercom identity verification
        let intercomUserHash = getIntercomUserHash(trackable_intercom.user_id ?? "")
        if (!!intercomUserHash) {
            trackable_intercom.user_hash = intercomUserHash
        }

        return {
            planName : '',
            mixpanelIdentify : user_data.email,
            trackableMixpanel : trackable_mixpanel,
            trackableIntercom : trackable_intercom
        }
    }

    let trackable_common = {
        'user_type': 'shop',
        'shop_created': shop_data.created_at,
        'shop_city' : shop_data.city,
        'shop_country': shop_data.country,
        'currency' : shop_data.currency,
        'shop_owner' : shop_data.shop_owner,
        'shop_name' : shop_data.name,
        'domain' : shop_data.domain,
        'customer_count' : shop_data.customer_count,
        'order_count' : shop_data.order_count,
        'product_count' : shop_data.product_count,
        'onboard_email': shop_data.onboard_email ?? '',
        'onboard_name': shop_data.onboard_name ?? '',
        'onboard_industry': shop_data.onboard_industry ?? '',
        'onboard_user_type': shop_data.onboard_user_type ?? '',
        'onboard_phone': shop_data.onboard_phone ?? '',
        'country_name': shop_data.country_name,
        'province_code': shop_data.province_code,
        'province': shop_data.province,
        'plan_display_name': shop_data.plan_display_name,
        'plan_name': shop_data.plan_name,
        'active_flag': 1,
    }

    let trackable_mixpanel = {
        '$name': !!shop_data.onboard_name ? shop_data.onboard_name : shop_data.shop_owner,
        '$phone': shop_data.phone,
        '$email' : !!shop_data.onboard_email ? shop_data.onboard_email : shop_data.customer_email,
        '$created': shop_data.row_created_at,
        ...trackable_common
    }

    let trackable_intercom = {
        'name': !!shop_data.onboard_name ? shop_data.onboard_name : shop_data.shop_owner,
        'phone': shop_data.phone,
        'email' : !!shop_data.onboard_email ? shop_data.onboard_email : shop_data.customer_email,
        'created_at': shop_data.row_created_at,
        'user_id' : shop_data.myshopify_domain,
        ...trackable_common
    }

    // Intercom identity verification
    let intercomUserHash = getIntercomUserHash(trackable_intercom.user_id ?? "")
    if (!!intercomUserHash) {
        trackable_intercom.user_hash = intercomUserHash
    }

    return {
        planName : (shop_data.plan_display_name ?? ''),
        mixpanelIdentify : shop_data.myshopify_domain,
        trackableMixpanel : trackable_mixpanel,
        trackableIntercom : trackable_intercom
    }
}

async function getLoginConfig({shop_data, authed_user, user_data, user_shops, is_admin, resolved_login_id}) {

    let isUserFlow = !!authed_user && !!authed_user.email
    let loginConfig = {
        userOnboarded : false,
        userData : {},
        allow_customer_export : false,
        subscription : {},
        linkUserShop : {},
        shopOptions: [],
        shop : {},
        shopOnboarded : false
    }

    if (isUserFlow && (!user_data || !user_data.uid)) {
        // Case prevented in resolveUser middleware
        // User Flow with no shop mapped yet - return empty config
        // User can do
        //  1. email-verification
        return loginConfig
    } else if (isUserFlow && (!user_shops || user_shops.length == 0) && (!shop_data || !shop_data.shop_id)) {
        // User Flow with no shop mapped yet - return empty config
        // User can do 
        //  1. email-verification
        //  2. onboarding
        //  3. add new store
        loginConfig.userData = user_data
        loginConfig.userOnboarded = !!user_data.onboard_email
        return loginConfig
    } else if (isUserFlow && shop_data && shop_data.shop_id &&  user_shops &&
        user_shops.filter((s) => s.shop_id == shop_data.shop_id).length == 0) {
        // User flow with shop_data - but not mapped to user
        // link-store flow - return empty config
        // User can do
        //  1. link-store
        loginConfig.userData = user_data
        loginConfig.userOnboarded = !!user_data.onboard_email
        loginConfig.linkUserShop = shop_data
        return loginConfig
    }


    let shopConfigShop = shop_data;
    if (isUserFlow && user_shops && user_shops.length > 0) {
        shopConfigShop = await getShopByOrigin(user_shops[0].myshopify_domain)
    }

    // shop_data is optional here - for user only flow
    let promises = [
        getShopOptions(shop_data, user_data, user_shops, is_admin),
        getShopConfig(shopConfigShop),
        getLoginTrackingConfig(shop_data, user_data),
        getFeedbackForOrigin(resolved_login_id)
    ];

    let [
        shopOptions,
        shopConfig,
        trackable,
        feedbacks
    ] = await Promise.all(promises)


    loginConfig = {
        ...loginConfig,
        ...shopOptions,
        ...trackable,
        // subscription,
        feedbacks,
        allow_customer_export : !isUserFlow || (user_data.workspace_type == WORKSPACE_TYPE_OWNER),
    }

    if (is_admin) {
        loginConfig.admin = is_admin
    }

    if (isUserFlow) {
        // user specific config - only present in user flow
        loginConfig.userData = user_data
        loginConfig.userOnboarded = !!user_data.onboard_email
        if (user_shops && user_shops.length > 0) {
            loginConfig.initSelectedShop = user_shops[0].myshopify_domain;
            loginConfig.initShopConfig = shopConfig
        }
    } else {
        // shop specific config - only present in shop flow
        loginConfig.shop = shop_data
        loginConfig.shopOnboarded = !!shop_data.onboard_email
        loginConfig.initShopConfig = shopConfig
    }

    return loginConfig;
}

// light weight query to check if shop is onboarded
async function bqSyncStatus(shop_id) {
    
    const useMigrationDataset = await shouldUseMigrationDataset(shop_id);
    const cubejsApi = cubejsInstance(shop_id, "ShopMetrics", useMigrationDataset)
    try {
        let resultSet = await cubejsApi.load({
            "measures": [
                "shop_metrics.count",
            ],
            "dimensions": [
                "shop_metrics.shop_id",
            ]
        });

        let shopResultsData = resultSet.tablePivot()
        if (shopResultsData.length != 0) {
            return 1
        }
    } catch (error) {
        logger.error(error)
        return 0
    }

    return 0
}

// returns config for selected shop - like subscription / sync status
// for authed shop - this is passed in loginConfig as initShopConfig
async function getShopConfig(shop) {

    if (shop.myshopify_domain == "consolidated.myshopify.com") {
        let aggSubscription = await getAggregatedActiveSubscription(shop.shop_id);
        return {
            shop : shop,
            subscription_enabled : true,
            useMigrationDataset : false,
            syncPercentage : 100,
            syncDone : true, // TODO - aggregated status
            planDetails : await getPlanDetails(aggSubscription.name ?? ""), // TODO - use aggregated plan details?
            lastSyncedAt : dayjs().fromNow()
        }
    }

    let promises = [
        getShopSyncStatus(shop),
        shouldUseMigrationDataset(shop.shop_id),
        getActivePlanDetails(shop.shop_id ?? '')
    ];

    let [syncStatus, useMigrationDataset, planDetails] = await Promise.all(promises)

    let featureContext = {
        myshopify_domain: shop.myshopify_domain,
        installed_at: dayjs(shop.row_created_at).format("YYYYMMDD"),
        country_name: shop.country_name,
        plan_display_name: shop.plan_display_name
    };

    let isSubscriptionEnabled = unleash.isEnabled('subscription_enabled', featureContext, function() {
        return true; // default to true if featureKey doesn't exist
    });

    return {
        shop : shop,
        useMigrationDataset,
        subscription_enabled : isSubscriptionEnabled,
        planDetails,
        ...syncStatus
    }
}


// get sync status of selectedShop in request
async function getShopSyncStatus(shop) {

    if (!shop || !shop.shop_id) {
        return {
            syncPercentage : 0,
            syncDone : false,
            lastSyncedAt : '-'
        }
    }

    let syncDone = (shop.last_sync_at && dayjs(shop.last_sync_at).isValid())

    const count = await fetchOrderCount(shop.shop_id)
    let percentage = count > 0 && shop.order_count > 0 ? Math.min(100, Math.round(100 * count/shop.order_count)) : 0;
    if (percentage == 100 && !syncDone) {
        percentage = 99
    } else if (percentage == 0) {
        percentage = 1
    } else if (percentage > 100) {
        percentage = 100
    }

    let isRecentInstall = dayjs.tz(shop.row_created_at, DATA_TIME_ZONE).unix() + 18000 > dayjs().unix();
    if (syncDone && isRecentInstall) {
        let bqSyncDone = await bqSyncStatus(shop.shop_id)
        if (bqSyncDone == 0) {
            syncDone = false
            percentage = 99
        } else {
            percentage = 100
        }
    }

    return {
        syncPercentage: percentage,
        syncDone: syncDone,
        lastSyncedAt : syncDone ? dayjs.tz(shop.last_sync_at, DATA_TIME_ZONE).fromNow() : '-',
    }
}

// middleware to resolve which shop's data to fetch
// this can be run independently or after the user.resolveUser middleware
// It accepts resolveUser variables - optionally to be present in req.body
async function resolveShop(req, res, next) {

    // accepts following variables from previous middleware like user.resolveShop
    let serverIsAdmin = !!req.serverAddedData && req.serverAddedData.is_admin;
    let serverConsolidatedShop = !!req.serverAddedData && req.serverAddedData.consolidated_shop ? req.serverAddedData.consolidated_shop : {};
    let serverUserSelectedShop = !!req.serverAddedData && req.serverAddedData.user_selected_shop ? req.serverAddedData.user_selected_shop : null;
    let serverResolvedLoginId = !!req.serverAddedData && req.serverAddedData.resolved_login_id ? req.serverAddedData.resolved_login_id : null;

    let authenticated_shop = '';
    // consolidated view shops are passed as array of shop ids for consolidated view in user session

    if (res.locals.shopify && res.locals.shopify.session && res.locals.shopify.session.shop) {
        authenticated_shop = res.locals.shopify.session.shop;
    }

    let shop_to_fetch = authenticated_shop;
    let isAdmin = (!!authenticated_shop && authenticated_shop == process.env.TEST_STORE) || serverIsAdmin;
    if (isAdmin) {
        var selectedShop = req.body.selectedShop || req.query.selectedShop;
        if (selectedShop) {
            shop_to_fetch = selectedShop
        }
    } else if (!!serverUserSelectedShop) {
        // valid for a user session - change shop_to_fetch to user_selected_shop
        shop_to_fetch = serverUserSelectedShop
    }

    let isDemoStore = shop_to_fetch == "demo-store-2023.myshopify.com";
    let consolidatedViewShops = [];
    // Hack to show consolidated view
    if ((isAdmin && shop_to_fetch == process.env.TEST_STORE) || isDemoStore) {
        // test data purpose or demo purpose
        consolidatedViewShops = ['26435276', '21723321', '7131234388'];
    }

    var shopSubscription = {};
    var shopObj = await getShopByOrigin(shop_to_fetch)
    var shopData = shopObj;
    var consolidatedShop = shopObj;
    if (!!authenticated_shop && authenticated_shop !== shop_to_fetch) {
        shopData = await getShopByOrigin(authenticated_shop)
    }
    
    if (!!serverConsolidatedShop && serverConsolidatedShop.myshopify_domain) {
        consolidatedShop = serverConsolidatedShop
        shopSubscription = await getAggregatedActiveSubscription(serverConsolidatedShop.shop_id);
    } else if (consolidatedViewShops && consolidatedViewShops.length > 0) {
        // only for woollywinter, demo-store-2023
        consolidatedShop = {
            name : shopObj.name,
            myshopify_domain : shopObj.myshopify_domain,
            shop_id : consolidatedViewShops,
            currency : shopObj.currency,
            is_fake_consolidated : true
        }
        shopSubscription = await getActiveSubscription(shopObj.shop_id);
    } else {
        shopSubscription = await getActiveSubscription(shopObj.shop_id);
    }

    req.body.shop_data = shopData; // authenticated shop
    req.body.shop = shopObj; // selected or authenticated shop - but not consolidated
    req.body.consolidated_shop = consolidatedShop; // selected or authenticated or consolidated shop
    req.body.is_admin = isAdmin;
    req.body.subscription = shopSubscription;
    if (!serverResolvedLoginId && !!shopData.myshopify_domain) {
        req.body.resolved_login_id = shopData.myshopify_domain;
    }

    await next();
}


// runs a graphql query on shopify using access_token and returns response
// gqlRequest =  {
//     query: `query {
//         shop {
//             name
//         }
//     }`,
//     variables: {}
// }
async function graphQlQuery(shop_origin, access_token, gqlRequest) {

    if (!gqlRequest || Object.keys(gqlRequest).length == 0) {
        return {}
    }

    try {
        const axiosInstance = axios.create();
        var {data} = await axiosInstance.post(
            `https://${shop_origin}/admin/api/${SHOPIFY_API_VERSION}/graphql.json`,
            gqlRequest,
            {
                headers: {
                  'Content-Type': 'application/json',
                  'X-Shopify-Access-Token' : access_token
                }
            }
        )
    
        return data;
    } catch (err) {
        logger.error('shop.graphQlQuery error : ' , err)
        return {}
    }
}

async function shoffiAffliate({shopName, xff}) {

    if (!shopName || !xff) {
        return false
    }

    logger.info('shoffiAffliate request : ', {shopName, xff})

    try {
        const axiosInstance = axios.create();
        var {data} = await axiosInstance.post(
            "https://platform.shoffi.app/v1/newMerchant",
            {
                api_key: "pk_yXsat64x",
                shopName,
                appId: "4766789",
                XFF: xff
            }, {
                headers: {
                    'Content-Type': 'application/json',
                }
            }
        );
        logger.info('shoffiAffliate response : ', {data, shopName, xff})
        return true;
    } catch (err) {
        logger.error('shop.shoffiAffliate error : ' , err)
        return false
    }
}

async function recordFeedback(origin, feedback) {
    let updatedFeedback = {
        ...(await getFeedbackForOrigin(origin)),
        ...feedback
    }

    try {
        return await redis.hset('feedbacks', origin, JSON.stringify(updatedFeedback))
    } catch (err) {
        logger.error('recordFeedback error - ', err)
        return null
    }
}

async function getFeedbackForOrigin(origin) {
    let val = null;
    try {
        val = await redis.hget('feedbacks', origin)
        if (val !== null) {
            return JSON.parse(val)
        }
    } catch (err) {
        logger.error('getFeedbackForOrigin error - ', err)
        return {}
    }

    return {}
}

function getBlacklistCacheKey() {
    let dt = dayjs() // irrespective of timezone
    let current_week = dt.format("YYYY-" + String("0" + dt.isoWeek()).slice(-2))
    return `blacklist_records_${current_week}`
}

async function blacklistFor(type, origin) {
    let blacklist_records = await getBlacklistRecords()
    blacklist_records[type].push(origin)
    cache.set(getBlacklistCacheKey(), blacklist_records, null)
}

async function getBlacklistRecords() {
    let default_records = {
        'review' : [],
        'subscribe' : []
    };

    let blacklist_records = await cache.get(getBlacklistCacheKey())
    if (!blacklist_records) {
        blacklist_records = default_records
    }
    return blacklist_records
}

async function isNewPricingEnabled(shop_id) {

    
    let shopObj = await getShopById(shop_id);
    if (!shopObj || !shopObj.shop_id) {
        return false;
    }

    let featureContext = {
        myshopify_domain: shopObj.myshopify_domain,
        installed_at: dayjs(shopObj.row_created_at).format("YYYYMMDD"),
        country_name: shopObj.country_name,
        plan_display_name: shopObj.plan_display_name
    };

    const new_pricing_model = unleash.isEnabled('new_pricing_model', featureContext);

    return new_pricing_model
}

export default {
    WORKSPACE_TYPE_OWNER,
    DATA_TIME_ZONE,
    SHOPIFY_API_VERSION,
    pushAsyncTask,
    processAsyncTask,
    shoffiAffliate,
    afterAuth,
    updatePartial,
    getShopById,
    getShopConfig,
    getLoginConfig,
    resolveShop,
    afterUninstall,
    getShopByOrigin,
    clearShopCache,
    saveOnboardingDetails,
    triggerCustomerEngagementChat,
    updateIntercomUser,
    recordFeedback,
    getBlacklistRecords,
    blacklistFor,
    updateShopState,
    getShopState,
    graphQlQuery,
    getShopifyClient,
    slackAlert,
    isNewPricingEnabled,
    shouldUseMigrationDataset
};