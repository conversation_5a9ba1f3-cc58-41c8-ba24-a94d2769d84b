import mysql from './database.js';
import logger from './logger.js';
import { cache } from './redis.js';
import dotenv from 'dotenv';
import shop from './shop.js';
import { VALID_COUPONS } from './subscription/plans.js';
dotenv.config();

function getSubCacheKey(shop_id) {
    return `active_subscription_v4_${shop_id}`;
}

export const getSubscriptionById = async (sub_id) => {
    try {
        let result = await mysql.query('SELECT * FROM subscriptions WHERE subscription_id = ? LIMIT 1', [sub_id]);
        return result[0] ?? {}
    } catch (err) {
        logger.error('subscription.getSubscriptionById error : ', err)
        return {}
    }
}

export const getActiveSubscriptionsFromShopify = async (shop_origin, accessToken) => {
    const query = {
        query: `query {
        currentAppInstallation {
                activeSubscriptions {
                    id
                    name
                    status
                    trialDays
                    createdAt
                }
            }
        }`
    };

    const responseJson = await shop.graphQlQuery(shop_origin, accessToken, query)
    if (!!responseJson && !!responseJson.data && !!responseJson.data.currentAppInstallation) {
        return responseJson.data.currentAppInstallation.activeSubscriptions ?? []
    }

    return []
};

// getAggregatedActiveSubscription is used for consolidated view
// it returns the first active subscription
// if there is a single shop with no active subscription, it returns {}
export const getAggregatedActiveSubscription = async (shop_ids) => {
    if (!Array.isArray(shop_ids) || shop_ids.length === 0) {
        return {};
    }

    let promises = [];
    let results = [];

    try {
        for (let shop_id of shop_ids) {
            promises.push(getActiveSubscription(shop_id));
        }

        results = await Promise.all(promises);
        if (results && results.length > 0) {
            for (let result of results) {
                if (!result || !result.subscription_id) {
                    return {}
                }
            }
        }
    } catch (err) {
        logger.error('subscription.getAggregatedActiveSubscription error : ', err);
        return {};
    }

    // return the first active subscription
    return results[0] ?? {};
}

export const getActiveSubscription = async (shop_id) => {

    if (!shop_id) {
        return {}
    }

    var cacheKey = getSubCacheKey(shop_id);
    var fromCache = await cache.get(cacheKey);
    if (fromCache !== null && fromCache !== undefined) {
        if (!!fromCache && !!fromCache.coupon && fromCache.coupon in VALID_COUPONS) {
            fromCache.couponDetails = VALID_COUPONS[fromCache.coupon];
        }
        return fromCache
    }

    var subs = {}
    try {
        let result = await mysql.query('SELECT * FROM subscriptions WHERE shop_id = ? and status = "ACTIVE"', [shop_id + '']);
        subs = result[0] ?? {}
    } catch (err) {
        logger.error('subscription.getActiveSubscription error : ', err)
        return {}
    }

    cache.set(cacheKey, subs)

    if (!!subs && !!subs.coupon && subs.coupon in VALID_COUPONS) {
        subs.couponDetails = VALID_COUPONS[subs.coupon];
    }

    return subs
}

export const updateSubscription = async (sub_id, status, shop_id) => {
    try {
        await mysql.query('UPDATE subscriptions SET status = ? WHERE subscription_id = ?', [status, sub_id]);
        var cacheKey = getSubCacheKey(shop_id);
        await cache.del(cacheKey)
        return true
    } catch (err) {
        logger.error('shop.updateSubscription error : ', err)
        return false
    }
}

export const saveSubscription = async (subscription_record) => {
    try {
        await mysql.query('INSERT INTO subscriptions SET ?', subscription_record);
        if (subscription_record && subscription_record.shop_id) {
            var cacheKey = getSubCacheKey(subscription_record.shop_id);
            await cache.del(cacheKey)
        }
        return true
    } catch (err) {
        logger.error('shop.saveSubscription error : ', err)
        return false
    }
}

export const upsertSubscription = async (subscription_record) => {
    let subscription  = await getSubscriptionById(subscription_record.subscription_id)

    if (subscription && subscription.subscription_id) {
        return await updateSubscription(subscription.subscription_id, subscription_record.status, subscription_record.shop_id)
    } else {
        return await saveSubscription(subscription_record)
    }
}