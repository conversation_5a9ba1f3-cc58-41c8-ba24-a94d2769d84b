import { createRequire } from "module";
const require = createRequire(import.meta.url);
import logger from "./logger.js";
import mysql from "./database.js";
import shop from "./shop.js";
import {cubejsAdminInstance} from "./cube/instance.js"
import dayjs from "dayjs";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import isoWeek from "dayjs/plugin/isoWeek";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(quarterOfYear);
dayjs.extend(isoWeek)
import util from './common/util.js';

// grab the Mixpanel factory
var Mixpanel = require('mixpanel');

// create an instance of the mixpanel client
var mixpanel = Mixpanel.init("93d9fbb11310729b4c8e6f60723ae10d", {
    protocol: "https"
});

const track = (shopOrigin, event_name, props = {}) => {
    return mixpanel.track(event_name, {
        distinct_id: shopOrigin,
        ...props
    })
}

function sleep(ms) {
    return new Promise((resolve) => { setTimeout(resolve, ms);});
}

async function fetchCubeMetrics() {
    const cubejsApi = cubejsAdminInstance()
    try {
        let resultSet = await cubejsApi.load({
            "dimensions": [
                "shop_metrics.shop_id",
                "shop_metrics.shop_currency",
                "Shops.myshopifyDomain"
            ],
            "measures": [
              "shop_metrics.order_count",
              "shop_metrics.total_price",
              "shop_metrics.cust_count"
            ],
            "timeDimensions": [{
                "dimension": "shop_metrics.date",
                "dateRange": "Last 30 days"
            }],
            "filters": [{
                "member": "shop_metrics.shop_currency",
                "operator": "set"
            },
            {
                "member": "shop_metrics.total_price",
                "operator": "gt",
                "values": [
                  "0"
                ]
            }]
        })

        let resultData = resultSet.tablePivot()
        let rowMap = {}

        for (var k in resultData) {
            let r = resultData[k]
            rowMap[r["shop_metrics.shop_id"]] = {
                shop_id : r["shop_metrics.shop_id"] ?? "",
                myshopify_domain: r["Shops.myshopifyDomain"] ?? "",
                shop_currency : r["shop_metrics.shop_currency"] ?? "",
                total_price : Math.round(parseFloat(r["shop_metrics.total_price"] ?? 0)),
                total_price_usd : await util.convertCurrencyUSD(
                    Math.round(parseFloat(r["shop_metrics.total_price"] ?? 0)),
                    r["shop_metrics.shop_currency"] ?? ""
                ),
                order_count : parseInt(r["shop_metrics.order_count"] ?? 0),
                cust_count : parseInt(r["shop_metrics.cust_count"] ?? 0),
            }
        }
        return Object.values(rowMap)
    } catch (error) {
        logger.error("Error in fetching shop metrics", error)
        return []
    }
}

const getSubscriptionMetrics = async () => {

    var query = `
        SELECT
            myshopify_domain,
            active_flag, 
            uninstalled_at,
            IF(
                agg_subscription_status = "ACTIVE", IF(NOW() < agg_trial_ends_at, "IN_TRIAL", "PAID"),
                agg_subscription_status
            ) as agg_subscription_status 
        FROM (
            SELECT s.myshopify_domain,
                s.active_flag,
                s.uninstalled_at,
                substring_index(
                    GROUP_CONCAT(subs.status ORDER BY FIELD(subs.status, "ACTIVE", "UNINSTALLED", "CANCELLED", "DECLINED", "EXPIRED", "PENDING")
                ), ",", 1) as agg_subscription_status,
                DATE_ADD(substring_index(
                    GROUP_CONCAT(subs.row_created_at ORDER BY FIELD(subs.status, "ACTIVE", "UNINSTALLED", "CANCELLED", "DECLINED", "EXPIRED", "PENDING")
                ), ",", 1), INTERVAL 7 DAY) as agg_trial_ends_at
            FROM shops s
            INNER JOIN subscriptions subs ON subs.shop_id = s.shop_id
            WHERE subs.name = 'Premium Plan Monthly'
            GROUP BY 1
        ) as x
    `;

    var data = []

    try {
        data = await mysql.query(query, []);
    } catch (err) {
        data = []
        logger.error('syncSubscriptionMetrics  error - ', err)
    }

    return data;
}

const getShopMetrics = async () => {

    var query = `
        SELECT 
            myshopify_domain,
            active_flag, 
            uninstalled_at,
            last_sync_at,
            onboard_name,
            onboard_email,
            onboard_industry,
            onboard_user_type
        FROM shops
        WHERE row_updated_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
    `;

    var data = []

    try {
        data = await mysql.query(query, []);
    } catch (err) {
        data = []
        logger.error('syncShopMetrics  error - ', err)
    }

    return data;
}

const syncDBMetrics = async (shouldUpdateIntercom = true) => {
    logger.info("syncDBMetrics started")
    try {
        let data = await getSubscriptionMetrics();
        let counter = 0
        for (var k in data) {
            let row = data[k]

            if (shouldUpdateIntercom) {
                await shop.updateIntercomUser(row.myshopify_domain, {
                    active_flag: row.active_flag,
                    uninstalled_at: row.uninstalled_at,
                    subscription_status: row.agg_subscription_status
                })
            }

            mixpanel.people.set(row.myshopify_domain, {
                active_flag: row.active_flag,
                uninstalled_at: row.uninstalled_at,
                subscription: row.agg_subscription_status
            }, {
                $ignore_time: true
            });
            counter += 1
            if (counter % 50 == 0) {
                logger.info(`syncDBMetrics 1 - done ${counter} out of ${data.length}`)
                await sleep(500)
            }
        }

        data = await getShopMetrics();
        counter = 0
        for (var k in data) {
            let row = data[k]

            if (shouldUpdateIntercom) {
                await shop.updateIntercomUser(row.myshopify_domain, {
                    active_flag: row.active_flag,
                    uninstalled_at: row.uninstalled_at,
                    onboard_name: row.onboard_name,
                    onboard_email: row.onboard_email,
                    onboard_industry: row.onboard_industry,
                    onboard_user_type: row.onboard_user_type
                })
            }

            mixpanel.people.set(row.myshopify_domain, {
                active_flag: row.active_flag,
                uninstalled_at: row.uninstalled_at,
                last_sync_at: row.last_sync_at,
                onboard_name: row.onboard_name,
                onboard_email: row.onboard_email,
                onboard_industry: row.onboard_industry,
                onboard_user_type: row.onboard_user_type
            }, {
                $ignore_time: true
            });
            counter += 1
            if (counter % 50 == 0) {
                logger.info(`syncDBMetrics 2 - done ${counter} out of ${data.length}`)
                await sleep(500)
            }
        }
    } catch (err) {
        logger.error('syncMetrics  error - ', {err})
    }
    logger.info("syncDBMetrics ended")
}

const syncCubeMetrics = async (shouldUpdateIntercom = true) => {
    try {
        logger.info("syncCubeMetrics started")
        let shops = await fetchCubeMetrics();
        let counter = 0;
        for (var k in shops) {
            if (shouldUpdateIntercom) {
                await shop.updateIntercomUser(shops[k].myshopify_domain, {
                    month_revenue : shops[k].total_price_usd,
                    month_orders : shops[k].order_count
                });
            }

            mixpanel.people.set(shops[k].myshopify_domain, {
                month_revenue : shops[k].total_price_usd,
                month_orders : shops[k].order_count
            }, {
                $ignore_time: true
            });

            counter += 1
            if (counter % 50 == 0) {
                logger.info(`syncCubeMetrics - done ${counter} out of ${shops.length}`)
                await sleep(500)
            }
        }
    } catch (err) {
        logger.error('syncCubeMetrics  error - ', {err})
    }
    logger.info("syncCubeMetrics ended")
}

const tracker = {track, syncDBMetrics, syncCubeMetrics};

export default tracker;